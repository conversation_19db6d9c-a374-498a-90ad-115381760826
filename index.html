<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NEPSE Advanced Charting - Professional Trading Platform</title>
    <meta name="description" content="Advanced NEPSE stock charting application with candlestick patterns, ICT, and SMC analysis for professional traders." />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Critical CSS for loading state -->
    <style>
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #0f172a;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #1e293b;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #e2e8f0;
        margin-top: 20px;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
      }
    </style>
  </head>
  <body class="bg-slate-900 text-slate-100">
    <!-- Loading screen -->
    <div id="loading">
      <div class="text-center">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading NEPSE Advanced Charting...</div>
      </div>
    </div>
    
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // Hide loading screen when app is ready
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => loading.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
