// Core data types for NEPSE charting application

export interface Company {
  company_id: number;
  name: string;
  symbol: string;
  shares?: string;
  paid_up?: string;
  paid_up_capital?: string;
  market_cap?: string;
  sector_id: number;
  is_active: boolean;
  listed_date?: string;
}

export interface StockPrice {
  price_id: number;
  company_id: number;
  date: string;
  open_price: number;
  high_price: number;
  low_price: number;
  close_price: number;
  volume: number;
  percentage_change?: number;
  traded_amount?: number;
}

// Chart data format for TradingView Lightweight Charts
export interface CandlestickData {
  time: string | number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface LineData {
  time: string | number;
  value: number;
}

// Timeframe definitions
export type TimeFrame = '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';

export interface TimeFrameConfig {
  label: string;
  value: TimeFrame;
  days: number | null; // null for ALL
  description: string;
}

// Chart configuration
export interface ChartConfig {
  symbol: string;
  timeframe: TimeFrame;
  showVolume: boolean;
  showGrid: boolean;
  theme: 'light' | 'dark';
  indicators: string[];
  patterns: string[];
}

// Technical Indicators
export interface TechnicalIndicator {
  indicator_id: number;
  company_id: number;
  date: string;
  indicator_type: string;
  value: number;
  parameters?: Record<string, any>;
  purpose: 'CACHE' | 'HISTORICAL' | 'DECISION';
  expiry_time?: string;
  created_at: string;
}

// Candlestick Patterns
export interface CandlestickPattern {
  id: string;
  name: string;
  type: 'single' | 'double' | 'triple';
  category: 'reversal' | 'continuation' | 'neutral';
  bullish: boolean;
  description: string;
  strength: number; // 1-10 scale
}

export interface PatternDetection {
  pattern: CandlestickPattern;
  timestamp: string | number;
  confidence: number; // 0-1 scale
  candles: CandlestickData[];
  description: string;
}

// ICT (Inner Circle Trader) Types
export interface OrderBlock {
  id: string;
  type: 'bullish' | 'bearish';
  high: number;
  low: number;
  timestamp: string | number;
  strength: number; // 1-10 scale
  mitigated: boolean;
  mitigation_timestamp?: string | number;
}

export interface FairValueGap {
  id: string;
  type: 'bullish' | 'bearish' | 'neutral';
  high: number;
  low: number;
  start_timestamp: string | number;
  end_timestamp?: string | number;
  filled: boolean;
  fill_percentage: number; // 0-100
}

export interface LiquidityLevel {
  id: string;
  type: 'buy_side' | 'sell_side';
  price: number;
  timestamp: string | number;
  strength: number;
  swept: boolean;
  sweep_timestamp?: string | number;
}

// SMC (Smart Money Concepts) Types
export interface MarketStructure {
  id: string;
  type: 'BOS' | 'ChoCH' | 'MSS'; // Break of Structure, Change of Character, Market Structure Shift
  direction: 'bullish' | 'bearish';
  timestamp: string | number;
  price: number;
  confirmed: boolean;
}

export interface LiquiditySweep {
  id: string;
  type: 'liquidity_grab' | 'stop_hunt';
  direction: 'up' | 'down';
  price: number;
  timestamp: string | number;
  volume: number;
  significance: number; // 1-10 scale
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Chart state management
export interface ChartState {
  selectedSymbol: string | null;
  timeframe: TimeFrame;
  isLoading: boolean;
  error: string | null;
  candlestickData: CandlestickData[];
  volumeData: LineData[];
  indicators: Record<string, LineData[]>;
  patterns: PatternDetection[];
  orderBlocks: OrderBlock[];
  fairValueGaps: FairValueGap[];
  liquidityLevels: LiquidityLevel[];
  marketStructure: MarketStructure[];
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'price_update' | 'pattern_detected' | 'market_status' | 'error';
  data: any;
  timestamp: string;
}

export interface LivePriceUpdate {
  symbol: string;
  price: number;
  change: number;
  change_percent: number;
  volume: number;
  timestamp: string;
}

// User preferences
export interface UserPreferences {
  theme: 'light' | 'dark';
  defaultTimeframe: TimeFrame;
  enablePatternAlerts: boolean;
  enableICTAnalysis: boolean;
  enableSMCAnalysis: boolean;
  chartLayout: 'single' | 'multi';
  notifications: {
    patterns: boolean;
    orderBlocks: boolean;
    liquiditySweeps: boolean;
    priceAlerts: boolean;
  };
}

// Error types
export interface ChartError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}
