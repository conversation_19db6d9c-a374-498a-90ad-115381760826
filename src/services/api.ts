import axios, { AxiosInstance, AxiosResponse } from 'axios'
import {
  ApiResponse,
  PaginatedResponse,
  Company,
  StockPrice,
  CandlestickData,
  TechnicalIndicator,
  TimeFrame
} from '@/types'
import { API_BASE_URL } from '@/constants'

// Create axios instance with default configuration
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      // Add auth token if available
      const token = localStorage.getItem('auth_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response
    },
    (error) => {
      // Handle common errors
      if (error.response?.status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('auth_token')
        window.location.href = '/login'
      }

      return Promise.reject(error)
    }
  )

  return instance
}

const api = createApiInstance()

// API Service Class
class ApiService {
  // Companies
  async getCompanies(params?: {
    search?: string
    sector_id?: number
    is_active?: boolean
    page?: number
    limit?: number
  }): Promise<PaginatedResponse<Company>> {
    const response = await api.get('/companies', { params })
    return response.data
  }

  async getCompany(symbol: string): Promise<ApiResponse<Company>> {
    const response = await api.get(`/companies/${symbol}`)
    return response.data
  }

  // Stock Prices
  async getStockPrices(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      limit?: number
    }
  ): Promise<ApiResponse<StockPrice[]>> {
    const response = await api.get(`/stocks/${symbol}/prices`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  async getCandlestickData(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      limit?: number
    }
  ): Promise<ApiResponse<CandlestickData[]>> {
    const response = await api.get(`/stocks/${symbol}/candlestick`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  async getHistoricalData(
    symbol: string,
    timeframe: TimeFrame,
    limit: number = 500
  ): Promise<CandlestickData[]> {
    const response = await api.get(`/stocks/${symbol}/candlestick`, {
      params: { timeframe, limit }
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || 'Failed to fetch historical data')
    }
  }

  async getLatestPrice(symbol: string): Promise<ApiResponse<StockPrice>> {
    const response = await api.get(`/stocks/${symbol}/latest`)
    return response.data
  }

  // Technical Indicators
  async getTechnicalIndicators(
    symbol: string,
    indicator_type: string,
    params?: {
      start_date?: string
      end_date?: string
      parameters?: Record<string, any>
    }
  ): Promise<ApiResponse<TechnicalIndicator[]>> {
    const response = await api.get(`/stocks/${symbol}/indicators/${indicator_type}`, {
      params
    })
    return response.data
  }

  async calculateIndicator(
    symbol: string,
    indicator_type: string,
    parameters: Record<string, any>
  ): Promise<ApiResponse<TechnicalIndicator[]>> {
    const response = await api.post(`/stocks/${symbol}/indicators/${indicator_type}`, {
      parameters
    })
    return response.data
  }

  // Candlestick Patterns
  async detectPatterns(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      pattern_types?: string[]
      min_confidence?: number
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/patterns`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  // ICT Analysis
  async getOrderBlocks(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      type?: 'bullish' | 'bearish'
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/ict/order-blocks`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  async getFairValueGaps(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      type?: 'bullish' | 'bearish' | 'neutral'
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/ict/fair-value-gaps`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  async getLiquidityLevels(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      type?: 'buy_side' | 'sell_side'
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/ict/liquidity`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  // SMC Analysis
  async getMarketStructure(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      type?: 'BOS' | 'ChoCH' | 'MSS'
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/smc/market-structure`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  async getLiquiditySweeps(
    symbol: string,
    timeframe: TimeFrame,
    params?: {
      start_date?: string
      end_date?: string
      type?: 'liquidity_grab' | 'stop_hunt'
    }
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/stocks/${symbol}/smc/liquidity-sweeps`, {
      params: { timeframe, ...params }
    })
    return response.data
  }

  // Market Data
  async getMarketSummary(): Promise<ApiResponse<any>> {
    const response = await api.get('/market/summary')
    return response.data
  }

  async getTopGainers(limit: number = 10): Promise<ApiResponse<any[]>> {
    const response = await api.get('/market/top-gainers', {
      params: { limit }
    })
    return response.data
  }

  async getTopLosers(limit: number = 10): Promise<ApiResponse<any[]>> {
    const response = await api.get('/market/top-losers', {
      params: { limit }
    })
    return response.data
  }

  async getTopVolume(limit: number = 10): Promise<ApiResponse<any[]>> {
    const response = await api.get('/market/top-volume', {
      params: { limit }
    })
    return response.data
  }

  // Search
  async searchStocks(query: string): Promise<ApiResponse<Company[]>> {
    const response = await api.get('/search/stocks', {
      params: { q: query }
    })
    return response.data
  }
}

// Create and export singleton instance
export const apiService = new ApiService()
export default apiService
