import { CandlestickData, PatternDetection, CandlestickPattern } from '@/types'

// Candlestick pattern definitions
export const PATTERN_DEFINITIONS: Record<string, CandlestickPattern> = {
  doji: {
    id: 'doji',
    name: 'Doji',
    type: 'single',
    category: 'neutral',
    bullish: false,
    description: 'Indecision pattern where open and close are nearly equal',
    strength: 6
  },
  hammer: {
    id: 'hammer',
    name: 'Hammer',
    type: 'single',
    category: 'reversal',
    bullish: true,
    description: 'Bullish reversal pattern with long lower shadow',
    strength: 7
  },
  shootingStar: {
    id: 'shooting_star',
    name: 'Shooting Star',
    type: 'single',
    category: 'reversal',
    bullish: false,
    description: 'Bearish reversal pattern with long upper shadow',
    strength: 7
  },
  bullishEngulfing: {
    id: 'bullish_engulfing',
    name: 'Bullish Engulfing',
    type: 'double',
    category: 'reversal',
    bullish: true,
    description: 'Strong bullish reversal where second candle engulfs first',
    strength: 8
  },
  bearishEngulfing: {
    id: 'bearish_engulfing',
    name: 'Bearish Engulfing',
    type: 'double',
    category: 'reversal',
    bullish: false,
    description: 'Strong bearish reversal where second candle engulfs first',
    strength: 8
  },
  morningStar: {
    id: 'morning_star',
    name: 'Morning Star',
    type: 'triple',
    category: 'reversal',
    bullish: true,
    description: 'Strong bullish reversal pattern with three candles',
    strength: 9
  },
  eveningStar: {
    id: 'evening_star',
    name: 'Evening Star',
    type: 'triple',
    category: 'reversal',
    bullish: false,
    description: 'Strong bearish reversal pattern with three candles',
    strength: 9
  }
}

export class PatternDetectionService {
  private minConfidence: number = 0.7

  constructor(minConfidence: number = 0.7) {
    this.minConfidence = minConfidence
  }

  // Main pattern detection function
  detectPatterns(data: CandlestickData[]): PatternDetection[] {
    const patterns: PatternDetection[] = []
    
    if (data.length < 3) return patterns

    for (let i = 2; i < data.length; i++) {
      // Single candlestick patterns
      const dojiPattern = this.detectDoji(data[i])
      if (dojiPattern) patterns.push(dojiPattern)

      const hammerPattern = this.detectHammer(data[i], data.slice(Math.max(0, i-5), i))
      if (hammerPattern) patterns.push(hammerPattern)

      const shootingStarPattern = this.detectShootingStar(data[i], data.slice(Math.max(0, i-5), i))
      if (shootingStarPattern) patterns.push(shootingStarPattern)

      // Two candlestick patterns
      if (i >= 1) {
        const engulfingPattern = this.detectEngulfing(data[i-1], data[i])
        if (engulfingPattern) patterns.push(engulfingPattern)
      }

      // Three candlestick patterns
      if (i >= 2) {
        const starPattern = this.detectStar(data[i-2], data[i-1], data[i])
        if (starPattern) patterns.push(starPattern)
      }
    }

    return patterns.filter(pattern => pattern.confidence >= this.minConfidence)
  }

  // Single candlestick pattern detection
  private detectDoji(candle: CandlestickData): PatternDetection | null {
    const bodySize = Math.abs(candle.close - candle.open)
    const range = candle.high - candle.low
    
    if (range === 0) return null
    
    const bodyRatio = bodySize / range
    
    // Doji: body is less than 10% of the total range
    if (bodyRatio <= 0.1) {
      const confidence = 1 - bodyRatio * 10 // Higher confidence for smaller body
      
      return {
        pattern: PATTERN_DEFINITIONS.doji,
        timestamp: candle.time,
        confidence: Math.min(confidence, 1),
        candles: [candle],
        description: `Doji pattern detected with ${(bodyRatio * 100).toFixed(1)}% body ratio`
      }
    }
    
    return null
  }

  private detectHammer(candle: CandlestickData, previousCandles: CandlestickData[]): PatternDetection | null {
    const bodySize = Math.abs(candle.close - candle.open)
    const range = candle.high - candle.low
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low
    const upperShadow = candle.high - Math.max(candle.open, candle.close)
    
    if (range === 0) return null
    
    // Hammer criteria:
    // 1. Lower shadow at least 2x the body size
    // 2. Upper shadow very small (less than body size)
    // 3. Body in upper half of the range
    // 4. Previous trend should be bearish
    
    const isHammer = lowerShadow >= bodySize * 2 && 
                     upperShadow <= bodySize && 
                     Math.min(candle.open, candle.close) > candle.low + range * 0.6
    
    if (isHammer && this.isPreviousTrendBearish(previousCandles)) {
      const confidence = Math.min(lowerShadow / (bodySize * 2), 1) * 0.8 + 0.2
      
      return {
        pattern: PATTERN_DEFINITIONS.hammer,
        timestamp: candle.time,
        confidence,
        candles: [candle],
        description: `Hammer pattern with ${(lowerShadow / bodySize).toFixed(1)}x lower shadow`
      }
    }
    
    return null
  }

  private detectShootingStar(candle: CandlestickData, previousCandles: CandlestickData[]): PatternDetection | null {
    const bodySize = Math.abs(candle.close - candle.open)
    const range = candle.high - candle.low
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low
    const upperShadow = candle.high - Math.max(candle.open, candle.close)
    
    if (range === 0) return null
    
    // Shooting Star criteria (opposite of hammer):
    // 1. Upper shadow at least 2x the body size
    // 2. Lower shadow very small
    // 3. Body in lower half of the range
    // 4. Previous trend should be bullish
    
    const isShootingStar = upperShadow >= bodySize * 2 && 
                          lowerShadow <= bodySize && 
                          Math.max(candle.open, candle.close) < candle.high - range * 0.6
    
    if (isShootingStar && this.isPreviousTrendBullish(previousCandles)) {
      const confidence = Math.min(upperShadow / (bodySize * 2), 1) * 0.8 + 0.2
      
      return {
        pattern: PATTERN_DEFINITIONS.shootingStar,
        timestamp: candle.time,
        confidence,
        candles: [candle],
        description: `Shooting Star pattern with ${(upperShadow / bodySize).toFixed(1)}x upper shadow`
      }
    }
    
    return null
  }

  // Two candlestick pattern detection
  private detectEngulfing(candle1: CandlestickData, candle2: CandlestickData): PatternDetection | null {
    const body1Size = Math.abs(candle1.close - candle1.open)
    const body2Size = Math.abs(candle2.close - candle2.open)
    
    const candle1Bullish = candle1.close > candle1.open
    const candle2Bullish = candle2.close > candle2.open
    
    // Engulfing criteria:
    // 1. Opposite colored candles
    // 2. Second candle's body completely engulfs first candle's body
    // 3. Second candle should be significantly larger
    
    if (candle1Bullish === candle2Bullish) return null
    
    const engulfs = candle2.open < Math.min(candle1.open, candle1.close) && 
                   candle2.close > Math.max(candle1.open, candle1.close)
    
    if (engulfs && body2Size > body1Size * 1.2) {
      const isBullish = candle2Bullish
      const pattern = isBullish ? PATTERN_DEFINITIONS.bullishEngulfing : PATTERN_DEFINITIONS.bearishEngulfing
      const confidence = Math.min(body2Size / body1Size / 3, 1) * 0.8 + 0.2
      
      return {
        pattern,
        timestamp: candle2.time,
        confidence,
        candles: [candle1, candle2],
        description: `${isBullish ? 'Bullish' : 'Bearish'} Engulfing with ${(body2Size / body1Size).toFixed(1)}x size ratio`
      }
    }
    
    return null
  }

  // Three candlestick pattern detection
  private detectStar(candle1: CandlestickData, candle2: CandlestickData, candle3: CandlestickData): PatternDetection | null {
    const candle1Bullish = candle1.close > candle1.open
    const candle3Bullish = candle3.close > candle3.open
    
    const body1Size = Math.abs(candle1.close - candle1.open)
    const body2Size = Math.abs(candle2.close - candle2.open)
    const body3Size = Math.abs(candle3.close - candle3.open)
    
    // Star pattern criteria:
    // 1. First and third candles are large and opposite colors
    // 2. Middle candle is small (doji-like)
    // 3. Middle candle gaps away from first candle
    // 4. Third candle closes well into first candle's body
    
    if (candle1Bullish === candle3Bullish) return null
    if (body2Size > Math.min(body1Size, body3Size) * 0.3) return null
    
    const morningStarCondition = !candle1Bullish && candle3Bullish && 
                                candle3.close > (candle1.open + candle1.close) / 2
    
    const eveningStarCondition = candle1Bullish && !candle3Bullish && 
                                candle3.close < (candle1.open + candle1.close) / 2
    
    if (morningStarCondition || eveningStarCondition) {
      const isMorningStar = morningStarCondition
      const pattern = isMorningStar ? PATTERN_DEFINITIONS.morningStar : PATTERN_DEFINITIONS.eveningStar
      const confidence = 0.8 // High confidence for three-candle patterns
      
      return {
        pattern,
        timestamp: candle3.time,
        confidence,
        candles: [candle1, candle2, candle3],
        description: `${isMorningStar ? 'Morning' : 'Evening'} Star pattern detected`
      }
    }
    
    return null
  }

  // Helper functions
  private isPreviousTrendBearish(candles: CandlestickData[]): boolean {
    if (candles.length < 3) return false
    
    let bearishCount = 0
    for (let i = candles.length - 3; i < candles.length; i++) {
      if (candles[i].close < candles[i].open) bearishCount++
    }
    
    return bearishCount >= 2
  }

  private isPreviousTrendBullish(candles: CandlestickData[]): boolean {
    if (candles.length < 3) return false
    
    let bullishCount = 0
    for (let i = candles.length - 3; i < candles.length; i++) {
      if (candles[i].close > candles[i].open) bullishCount++
    }
    
    return bullishCount >= 2
  }

  // Set minimum confidence threshold
  setMinConfidence(confidence: number): void {
    this.minConfidence = Math.max(0, Math.min(1, confidence))
  }
}

export const patternDetectionService = new PatternDetectionService()
