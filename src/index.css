@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for the NEPSE charting application */

@layer base {
  * {
    @apply border-slate-700;
  }

  body {
    @apply bg-slate-900 text-slate-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-800;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-slate-600 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-500;
  }

  /* Chart container styles */
  .chart-container {
    @apply relative w-full h-full bg-slate-900 rounded-lg overflow-hidden;
    min-height: 400px;
  }

  /* Button variants */
  .btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900 transition-colors duration-200;
  }

  .btn-secondary {
    @apply px-4 py-2 bg-slate-700 text-slate-200 rounded-md hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-slate-900 transition-colors duration-200;
  }

  .btn-ghost {
    @apply px-4 py-2 text-slate-300 rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-slate-900 transition-colors duration-200;
  }

  /* Input styles */
  .input-primary {
    @apply w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
  }

  /* Card styles */
  .card {
    @apply bg-slate-800 border border-slate-700 rounded-lg p-4 shadow-lg;
  }

  .card-header {
    @apply border-b border-slate-700 pb-3 mb-4;
  }

  /* Status indicators */
  .status-bullish {
    @apply text-green-400;
  }

  .status-bearish {
    @apply text-red-400;
  }

  .status-neutral {
    @apply text-slate-400;
  }

  /* Loading animations */
  .loading-pulse {
    @apply animate-pulse bg-slate-700 rounded;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-slate-600 border-t-blue-500;
  }

  /* Pattern highlight styles */
  .pattern-bullish {
    @apply bg-green-500/20 border border-green-500/50;
  }

  .pattern-bearish {
    @apply bg-red-500/20 border border-red-500/50;
  }

  .pattern-neutral {
    @apply bg-slate-500/20 border border-slate-500/50;
  }

  /* ICT/SMC indicator styles */
  .order-block {
    @apply absolute pointer-events-none;
  }

  .order-block-bullish {
    @apply bg-green-500/30 border-l-2 border-green-500;
  }

  .order-block-bearish {
    @apply bg-red-500/30 border-l-2 border-red-500;
  }

  .fair-value-gap {
    @apply absolute pointer-events-none bg-blue-500/20 border border-blue-500/50;
  }

  .liquidity-level {
    @apply absolute pointer-events-none border-dashed;
  }

  .liquidity-buy-side {
    @apply border-green-500;
  }

  .liquidity-sell-side {
    @apply border-red-500;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
  }

  /* Chart-specific utilities */
  .chart-tooltip {
    @apply absolute z-50 px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-sm text-slate-200 shadow-lg pointer-events-none;
  }

  .chart-crosshair {
    @apply absolute pointer-events-none;
  }

  .chart-crosshair-line {
    @apply absolute bg-slate-500/50;
  }
}

/* Custom animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Professional Chart Styles */
.chart-container {
  background: #1a1a1a;
  position: relative;
}

.chart-toolbar {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-bottom: 1px solid #374151;
}

.chart-sidebar {
  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
  border-left: 1px solid #374151;
}

/* Trading View Style Buttons */
.tv-button {
  @apply px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200;
  @apply bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white;
  @apply border border-gray-600 hover:border-gray-500;
}

.tv-button.active {
  @apply bg-blue-600 text-white border-blue-500;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3);
}

/* Chart Loading Animation */
.chart-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Professional Indicators */
.indicator-item {
  @apply flex items-center justify-between p-3 bg-gray-800 rounded-lg;
  @apply hover:bg-gray-700 transition-colors cursor-pointer;
  border: 1px solid transparent;
}

.indicator-item:hover {
  border-color: #4b5563;
}

.indicator-item.active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Status indicators */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot.green {
  @apply bg-green-400;
  box-shadow: 0 0 6px rgba(34, 197, 94, 0.6);
}

.status-dot.red {
  @apply bg-red-400;
  box-shadow: 0 0 6px rgba(239, 68, 68, 0.6);
}

.status-dot.yellow {
  @apply bg-yellow-400;
  box-shadow: 0 0 6px rgba(251, 191, 36, 0.6);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .chart-container {
    @apply bg-white;
  }
}
