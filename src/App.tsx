
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import Layout from './components/Layout/Layout'
import Dashboard from './pages/Dashboard'
import ChartPage from './pages/ChartPage'
import PatternsPage from './pages/PatternsPage'
import StrategiesPage from './pages/StrategiesPage'
import BacktestingPage from './pages/BacktestingPage'
import SettingsPage from './pages/SettingsPage'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-slate-900 text-slate-100">
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/chart" element={<ChartPage />} />
            <Route path="/chart/:symbol" element={<ChartPage />} />
            <Route path="/patterns" element={<PatternsPage />} />
            <Route path="/strategies" element={<StrategiesPage />} />
            <Route path="/backtesting" element={<BacktestingPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </Layout>

        {/* Global toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1e293b',
              color: '#e2e8f0',
              border: '1px solid #475569',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#1e293b',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#1e293b',
              },
            },
          }}
        />
      </div>
    </Router>
  )
}

export default App
