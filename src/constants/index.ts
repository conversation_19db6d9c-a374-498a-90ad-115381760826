import { TimeFrameConfig } from '@/types'

// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api'
export const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:5001'

// Chart Configuration
export const CHART_CONFIG = {
  DEFAULT_HEIGHT: 600,
  MIN_HEIGHT: 400,
  MAX_HEIGHT: 1200,
  DEFAULT_THEME: 'dark' as const,
  GRID_COLOR: '#334155',
  TEXT_COLOR: '#e2e8f0',
  BACKGROUND_COLOR: '#0f172a',
  CROSSHAIR_COLOR: '#64748b',
}

// Color Schemes
export const COLORS = {
  BULLISH: {
    LIGHT: '#10b981',
    DEFAULT: '#059669',
    DARK: '#047857',
  },
  BEARISH: {
    LIGHT: '#ef4444',
    DEFAULT: '#dc2626',
    DARK: '#b91c1c',
  },
  NEUTRAL: {
    LIGHT: '#6b7280',
    DEFAULT: '#4b5563',
    DARK: '#374151',
  },
  VOLUME: {
    UP: '#10b981',
    DOWN: '#ef4444',
  },
  INDICATORS: {
    MA: '#3b82f6',
    RSI: '#8b5cf6',
    MACD: '#f59e0b',
    BOLLINGER: '#06b6d4',
  },
}

// Timeframe Configurations
export const TIMEFRAMES: TimeFrameConfig[] = [
  {
    label: '1D',
    value: '1D',
    days: 1,
    description: 'Last 24 hours',
  },
  {
    label: '1W',
    value: '1W',
    days: 7,
    description: 'Last week',
  },
  {
    label: '1M',
    value: '1M',
    days: 30,
    description: 'Last month',
  },
  {
    label: '3M',
    value: '3M',
    days: 90,
    description: 'Last 3 months',
  },
  {
    label: '6M',
    value: '6M',
    days: 180,
    description: 'Last 6 months',
  },
  {
    label: '1Y',
    value: '1Y',
    days: 365,
    description: 'Last year',
  },
  {
    label: 'ALL',
    value: 'ALL',
    days: null,
    description: 'All available data',
  },
]

// Candlestick Pattern Definitions
export const CANDLESTICK_PATTERNS = {
  // Single Candlestick Patterns
  DOJI: {
    id: 'doji',
    name: 'Doji',
    type: 'single',
    category: 'neutral',
    description: 'Indecision pattern with small body',
  },
  HAMMER: {
    id: 'hammer',
    name: 'Hammer',
    type: 'single',
    category: 'reversal',
    description: 'Bullish reversal pattern with long lower shadow',
  },
  SHOOTING_STAR: {
    id: 'shooting_star',
    name: 'Shooting Star',
    type: 'single',
    category: 'reversal',
    description: 'Bearish reversal pattern with long upper shadow',
  },
  MARUBOZU: {
    id: 'marubozu',
    name: 'Marubozu',
    type: 'single',
    category: 'continuation',
    description: 'Strong directional pattern with no shadows',
  },

  // Two Candlestick Patterns
  BULLISH_ENGULFING: {
    id: 'bullish_engulfing',
    name: 'Bullish Engulfing',
    type: 'double',
    category: 'reversal',
    description: 'Bullish reversal pattern where second candle engulfs first',
  },
  BEARISH_ENGULFING: {
    id: 'bearish_engulfing',
    name: 'Bearish Engulfing',
    type: 'double',
    category: 'reversal',
    description: 'Bearish reversal pattern where second candle engulfs first',
  },
  HARAMI: {
    id: 'harami',
    name: 'Harami',
    type: 'double',
    category: 'reversal',
    description: 'Reversal pattern where second candle is contained within first',
  },

  // Three Candlestick Patterns
  MORNING_STAR: {
    id: 'morning_star',
    name: 'Morning Star',
    type: 'triple',
    category: 'reversal',
    description: 'Bullish reversal pattern with three candles',
  },
  EVENING_STAR: {
    id: 'evening_star',
    name: 'Evening Star',
    type: 'triple',
    category: 'reversal',
    description: 'Bearish reversal pattern with three candles',
  },
  THREE_WHITE_SOLDIERS: {
    id: 'three_white_soldiers',
    name: 'Three White Soldiers',
    type: 'triple',
    category: 'continuation',
    description: 'Strong bullish continuation pattern',
  },
} as const

// ICT Concepts
export const ICT_CONCEPTS = {
  ORDER_BLOCKS: {
    BULLISH: 'bullish_order_block',
    BEARISH: 'bearish_order_block',
  },
  FAIR_VALUE_GAPS: {
    BULLISH: 'bullish_fvg',
    BEARISH: 'bearish_fvg',
    NEUTRAL: 'neutral_fvg',
  },
  LIQUIDITY: {
    BUY_SIDE: 'buy_side_liquidity',
    SELL_SIDE: 'sell_side_liquidity',
  },
  KILL_ZONES: {
    LONDON: { start: '02:00', end: '05:00', timezone: 'GMT' },
    NEW_YORK: { start: '07:00', end: '10:00', timezone: 'GMT' },
    ASIAN: { start: '20:00', end: '00:00', timezone: 'GMT' },
  },
} as const

// SMC Concepts
export const SMC_CONCEPTS = {
  MARKET_STRUCTURE: {
    BOS: 'break_of_structure',
    CHOCH: 'change_of_character',
    MSS: 'market_structure_shift',
  },
  LIQUIDITY_SWEEPS: {
    GRAB: 'liquidity_grab',
    HUNT: 'stop_hunt',
  },
  ZONES: {
    PREMIUM: 'premium_zone',
    DISCOUNT: 'discount_zone',
    EQUILIBRIUM: 'equilibrium_zone',
  },
} as const

// Technical Indicators
export const TECHNICAL_INDICATORS = {
  TREND: {
    SMA: 'simple_moving_average',
    EMA: 'exponential_moving_average',
    WMA: 'weighted_moving_average',
    VWMA: 'volume_weighted_moving_average',
  },
  MOMENTUM: {
    RSI: 'relative_strength_index',
    MACD: 'moving_average_convergence_divergence',
    STOCHASTIC: 'stochastic_oscillator',
    WILLIAMS_R: 'williams_percent_r',
  },
  VOLATILITY: {
    BOLLINGER_BANDS: 'bollinger_bands',
    ATR: 'average_true_range',
    KELTNER_CHANNELS: 'keltner_channels',
  },
  VOLUME: {
    VWAP: 'volume_weighted_average_price',
    OBV: 'on_balance_volume',
    MFI: 'money_flow_index',
  },
} as const

// Default Settings
export const DEFAULT_SETTINGS = {
  CHART: {
    THEME: 'dark',
    TIMEFRAME: '1D',
    SHOW_VOLUME: true,
    SHOW_GRID: true,
    SHOW_CROSSHAIR: true,
  },
  PATTERNS: {
    ENABLE_DETECTION: true,
    MIN_CONFIDENCE: 0.7,
    SHOW_LABELS: true,
    ALERT_ON_DETECTION: false,
  },
  ICT: {
    ENABLE_ANALYSIS: true,
    SHOW_ORDER_BLOCKS: true,
    SHOW_FVG: true,
    SHOW_LIQUIDITY: true,
    SHOW_KILL_ZONES: false,
  },
  SMC: {
    ENABLE_ANALYSIS: true,
    SHOW_MARKET_STRUCTURE: true,
    SHOW_LIQUIDITY_SWEEPS: true,
    SHOW_ZONES: true,
  },
} as const

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  DATA_FETCH_ERROR: 'Failed to fetch data. Please try again.',
  INVALID_SYMBOL: 'Invalid stock symbol. Please select a valid symbol.',
  CHART_RENDER_ERROR: 'Failed to render chart. Please refresh the page.',
  WEBSOCKET_ERROR: 'Real-time connection lost. Attempting to reconnect...',
} as const

// Success Messages
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Data loaded successfully',
  PATTERN_DETECTED: 'New pattern detected',
  SETTINGS_SAVED: 'Settings saved successfully',
  STRATEGY_CREATED: 'Strategy created successfully',
} as const
