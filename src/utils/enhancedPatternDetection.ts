interface CandlestickData {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

interface DetectedPattern {
  id: string
  name: string
  type: 'bullish' | 'bearish' | 'neutral'
  confidence: number
  time: string
  price: number
  description: string
  candleIndex: number
  significance: 'high' | 'medium' | 'low'
}

export class EnhancedPatternDetector {
  private data: CandlestickData[]
  
  constructor(data: CandlestickData[]) {
    this.data = data
  }

  detectAllPatterns(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    // Single candlestick patterns
    patterns.push(...this.detectDoji())
    patterns.push(...this.detectHammer())
    patterns.push(...this.detectShootingStar())
    patterns.push(...this.detectHangingMan())
    patterns.push(...this.detectInvertedHammer())
    
    // Double candlestick patterns
    patterns.push(...this.detectEngulfingPatterns())
    patterns.push(...this.detectPiercingLine())
    patterns.push(...this.detectDarkCloud())
    
    // Triple candlestick patterns
    patterns.push(...this.detectMorningStar())
    patterns.push(...this.detectEveningStar())
    patterns.push(...this.detectThreeWhiteSoldiers())
    patterns.push(...this.detectThreeBlackCrows())
    
    return patterns.sort((a, b) => b.confidence - a.confidence)
  }

  private detectDoji(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 1; i < this.data.length - 1; i++) {
      const candle = this.data[i]
      const bodySize = Math.abs(candle.close - candle.open)
      const totalRange = candle.high - candle.low
      
      if (totalRange === 0) continue
      
      const bodyRatio = bodySize / totalRange
      
      // Doji: very small body relative to total range
      if (bodyRatio <= 0.1) {
        const upperShadow = candle.high - Math.max(candle.open, candle.close)
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low
        
        let confidence = 70
        let patternName = 'Doji'
        
        // Dragonfly Doji
        if (upperShadow <= totalRange * 0.1 && lowerShadow >= totalRange * 0.6) {
          confidence = 85
          patternName = 'Dragonfly Doji'
        }
        // Gravestone Doji
        else if (lowerShadow <= totalRange * 0.1 && upperShadow >= totalRange * 0.6) {
          confidence = 85
          patternName = 'Gravestone Doji'
        }
        // Long-legged Doji
        else if (upperShadow >= totalRange * 0.3 && lowerShadow >= totalRange * 0.3) {
          confidence = 80
          patternName = 'Long-legged Doji'
        }
        
        patterns.push({
          id: `doji_${i}`,
          name: patternName,
          type: 'neutral',
          confidence,
          time: candle.time,
          price: (candle.high + candle.low) / 2,
          description: `${patternName} indicates market indecision and potential reversal`,
          candleIndex: i,
          significance: confidence >= 80 ? 'high' : 'medium'
        })
      }
    }
    
    return patterns
  }

  private detectHammer(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i]
      const prevCandle = this.data[i - 1]
      
      const bodySize = Math.abs(candle.close - candle.open)
      const totalRange = candle.high - candle.low
      const lowerShadow = Math.min(candle.open, candle.close) - candle.low
      const upperShadow = candle.high - Math.max(candle.open, candle.close)
      
      if (totalRange === 0) continue
      
      // Hammer criteria
      if (
        lowerShadow >= bodySize * 2 && // Long lower shadow
        upperShadow <= bodySize * 0.5 && // Small upper shadow
        bodySize <= totalRange * 0.3 && // Small body
        prevCandle.close > prevCandle.open // Previous candle was bearish trend
      ) {
        const confidence = Math.min(90, 60 + (lowerShadow / bodySize) * 5)
        
        patterns.push({
          id: `hammer_${i}`,
          name: 'Hammer',
          type: 'bullish',
          confidence,
          time: candle.time,
          price: candle.low,
          description: 'Hammer pattern suggests potential bullish reversal',
          candleIndex: i,
          significance: confidence >= 80 ? 'high' : 'medium'
        })
      }
    }
    
    return patterns
  }

  private detectShootingStar(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i]
      const prevCandle = this.data[i - 1]
      
      const bodySize = Math.abs(candle.close - candle.open)
      const totalRange = candle.high - candle.low
      const upperShadow = candle.high - Math.max(candle.open, candle.close)
      const lowerShadow = Math.min(candle.open, candle.close) - candle.low
      
      if (totalRange === 0) continue
      
      // Shooting Star criteria
      if (
        upperShadow >= bodySize * 2 && // Long upper shadow
        lowerShadow <= bodySize * 0.5 && // Small lower shadow
        bodySize <= totalRange * 0.3 && // Small body
        prevCandle.close > prevCandle.open // Previous candle was bullish
      ) {
        const confidence = Math.min(90, 60 + (upperShadow / bodySize) * 5)
        
        patterns.push({
          id: `shooting_star_${i}`,
          name: 'Shooting Star',
          type: 'bearish',
          confidence,
          time: candle.time,
          price: candle.high,
          description: 'Shooting Star pattern suggests potential bearish reversal',
          candleIndex: i,
          significance: confidence >= 80 ? 'high' : 'medium'
        })
      }
    }
    
    return patterns
  }

  private detectEngulfingPatterns(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 1; i < this.data.length; i++) {
      const current = this.data[i]
      const previous = this.data[i - 1]
      
      const currentBody = Math.abs(current.close - current.open)
      const previousBody = Math.abs(previous.close - previous.open)
      
      // Bullish Engulfing
      if (
        previous.close < previous.open && // Previous candle bearish
        current.close > current.open && // Current candle bullish
        current.open < previous.close && // Current opens below previous close
        current.close > previous.open && // Current closes above previous open
        currentBody > previousBody * 1.1 // Current body larger
      ) {
        patterns.push({
          id: `bullish_engulfing_${i}`,
          name: 'Bullish Engulfing',
          type: 'bullish',
          confidence: 85,
          time: current.time,
          price: (current.open + current.close) / 2,
          description: 'Bullish Engulfing pattern indicates strong buying pressure',
          candleIndex: i,
          significance: 'high'
        })
      }
      
      // Bearish Engulfing
      if (
        previous.close > previous.open && // Previous candle bullish
        current.close < current.open && // Current candle bearish
        current.open > previous.close && // Current opens above previous close
        current.close < previous.open && // Current closes below previous open
        currentBody > previousBody * 1.1 // Current body larger
      ) {
        patterns.push({
          id: `bearish_engulfing_${i}`,
          name: 'Bearish Engulfing',
          type: 'bearish',
          confidence: 85,
          time: current.time,
          price: (current.open + current.close) / 2,
          description: 'Bearish Engulfing pattern indicates strong selling pressure',
          candleIndex: i,
          significance: 'high'
        })
      }
    }
    
    return patterns
  }

  private detectMorningStar(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 2; i < this.data.length; i++) {
      const first = this.data[i - 2]
      const second = this.data[i - 1]
      const third = this.data[i]
      
      // Morning Star criteria
      if (
        first.close < first.open && // First candle bearish
        Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second candle small body
        third.close > third.open && // Third candle bullish
        third.close > (first.open + first.close) / 2 // Third closes above first's midpoint
      ) {
        patterns.push({
          id: `morning_star_${i}`,
          name: 'Morning Star',
          type: 'bullish',
          confidence: 88,
          time: third.time,
          price: (second.high + second.low) / 2,
          description: 'Morning Star pattern indicates strong bullish reversal',
          candleIndex: i - 1,
          significance: 'high'
        })
      }
    }
    
    return patterns
  }

  private detectEveningStar(): DetectedPattern[] {
    const patterns: DetectedPattern[] = []
    
    for (let i = 2; i < this.data.length; i++) {
      const first = this.data[i - 2]
      const second = this.data[i - 1]
      const third = this.data[i]
      
      // Evening Star criteria
      if (
        first.close > first.open && // First candle bullish
        Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second candle small body
        third.close < third.open && // Third candle bearish
        third.close < (first.open + first.close) / 2 // Third closes below first's midpoint
      ) {
        patterns.push({
          id: `evening_star_${i}`,
          name: 'Evening Star',
          type: 'bearish',
          confidence: 88,
          time: third.time,
          price: (second.high + second.low) / 2,
          description: 'Evening Star pattern indicates strong bearish reversal',
          candleIndex: i - 1,
          significance: 'high'
        })
      }
    }
    
    return patterns
  }

  // Placeholder methods for other patterns
  private detectHangingMan(): DetectedPattern[] { return [] }
  private detectInvertedHammer(): DetectedPattern[] { return [] }
  private detectPiercingLine(): DetectedPattern[] { return [] }
  private detectDarkCloud(): DetectedPattern[] { return [] }
  private detectThreeWhiteSoldiers(): DetectedPattern[] { return [] }
  private detectThreeBlackCrows(): DetectedPattern[] { return [] }
}

export const detectCandlestickPatterns = (data: CandlestickData[]): DetectedPattern[] => {
  const detector = new EnhancedPatternDetector(data)
  return detector.detectAllPatterns()
}
