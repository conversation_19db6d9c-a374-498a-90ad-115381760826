import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  BarChart3, 
  TrendingUp, 
  Search, 
  Settings, 
  Menu, 
  X,
  Activity,
  Target,
  Brain,
  TestTube
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    {
      name: 'Dashboard',
      href: '/',
      icon: BarChart3,
      description: 'Market overview and summary'
    },
    {
      name: 'Chart',
      href: '/chart',
      icon: TrendingUp,
      description: 'Advanced charting with indicators'
    },
    {
      name: 'Patterns',
      href: '/patterns',
      icon: Activity,
      description: 'Candlestick pattern analysis'
    },
    {
      name: 'Strategies',
      href: '/strategies',
      icon: Target,
      description: 'ICT & SMC trading strategies'
    },
    {
      name: 'Backtesting',
      href: '/backtesting',
      icon: TestTube,
      description: 'Strategy backtesting engine'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      description: 'Application preferences'
    },
  ]

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  return (
    <div className="min-h-screen bg-slate-900 flex">
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : '-100%',
        }}
        className="fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 border-r border-slate-700 lg:static lg:translate-x-0 lg:z-auto"
      >
        <div className="flex h-full flex-col">
          {/* Logo and close button */}
          <div className="flex items-center justify-between p-4 border-b border-slate-700">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-blue-500" />
              <div>
                <h1 className="text-lg font-bold text-white">NEPSE</h1>
                <p className="text-xs text-slate-400">Advanced Charting</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200
                    ${active
                      ? 'bg-blue-600 text-white'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                    }
                  `}
                >
                  <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                  <div className="flex-1">
                    <div>{item.name}</div>
                    <div className="text-xs opacity-75 mt-0.5">
                      {item.description}
                    </div>
                  </div>
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-slate-700">
            <div className="text-xs text-slate-400 text-center">
              <p>NEPSE Advanced Charting v1.0</p>
              <p className="mt-1">Professional Trading Platform</p>
            </div>
          </div>
        </div>
      </motion.aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top header */}
        <header className="bg-slate-800 border-b border-slate-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Mobile menu button */}
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white"
              >
                <Menu className="h-5 w-5" />
              </button>

              {/* Page title */}
              <div>
                <h2 className="text-lg font-semibold text-white">
                  {navigation.find(item => isActive(item.href))?.name || 'NEPSE Charting'}
                </h2>
                <p className="text-sm text-slate-400">
                  {navigation.find(item => isActive(item.href))?.description || 'Advanced financial analysis'}
                </p>
              </div>
            </div>

            {/* Header actions */}
            <div className="flex items-center space-x-3">
              {/* Search */}
              <button className="p-2 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors">
                <Search className="h-5 w-5" />
              </button>

              {/* Market status indicator */}
              <div className="flex items-center space-x-2 px-3 py-1 bg-slate-700 rounded-md">
                <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-slate-300">Market Closed</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default Layout
