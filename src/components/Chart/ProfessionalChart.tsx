import React, { useEffect, useRef, useState } from 'react'
import { create<PERSON>hart, IChartApi, ISeriesApi, Time, LineStyle } from 'lightweight-charts'
import PatternChips from './PatternChips'
import { detectCandlestickPatterns } from '@/utils/enhancedPatternDetection'

interface ProfessionalChartProps {
  symbol: string
  height?: number
}

const ProfessionalChart: React.FC<ProfessionalChartProps> = ({
  symbol,
  height = 600
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)
  const macdSeriesRef = useRef<ISeriesApi<'Line'> | null>(null)
  const macdSignalSeriesRef = useRef<ISeriesApi<'Line'> | null>(null)
  const macdHistogramSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)

  const [isLoading, setIsLoading] = useState(true)
  const [stockData, setStockData] = useState<any>(null)
  const [patterns, setPatterns] = useState<any[]>([])
  const [chartDimensions, setChartDimensions] = useState({ width: 0, height: 0 })

  useEffect(() => {
    if (!chartContainerRef.current) return

    // Create main chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: height,
      layout: {
        background: { color: '#1a1a1a' },
        textColor: '#d1d4dc',
        fontSize: 12,
        fontFamily: 'Trebuchet MS, sans-serif',
      },
      grid: {
        vertLines: {
          color: '#2a2a2a',
          style: LineStyle.Solid,
          visible: true,
        },
        horzLines: {
          color: '#2a2a2a',
          style: LineStyle.Solid,
          visible: true,
        },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          color: '#758696',
          width: 1,
          style: LineStyle.Dashed,
          labelBackgroundColor: '#4c525e',
        },
        horzLine: {
          color: '#758696',
          width: 1,
          style: LineStyle.Dashed,
          labelBackgroundColor: '#4c525e',
        },
      },
      rightPriceScale: {
        borderColor: '#485c7b',
        textColor: '#d1d4dc',
        entireTextOnly: false,
        visible: true,
        borderVisible: true,
        scaleMargins: {
          top: 0.1,
          bottom: 0.4,
        },
      },
      timeScale: {
        borderColor: '#485c7b',
        textColor: '#d1d4dc',
        timeVisible: true,
        secondsVisible: false,
        borderVisible: true,
        rightOffset: 5,
        barSpacing: 8,
        fixLeftEdge: false,
        lockVisibleTimeRangeOnResize: true,
      },
      watermark: {
        visible: true,
        fontSize: 48,
        horzAlign: 'center',
        vertAlign: 'center',
        color: 'rgba(171, 71, 188, 0.3)',
        text: symbol,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 2,
        minMove: 0.01,
      },
    })

    candlestickSeriesRef.current = candlestickSeries

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: 'volume',
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    })

    volumeSeriesRef.current = volumeSeries

    // Configure volume price scale
    chart.priceScale('volume').applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    })

    // Add MACD indicator (will be added to separate pane)
    const macdSeries = chart.addLineSeries({
      color: '#2196F3',
      lineWidth: 2,
      priceScaleId: 'macd',
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    })

    const macdSignalSeries = chart.addLineSeries({
      color: '#FF6D00',
      lineWidth: 2,
      priceScaleId: 'macd',
    })

    const macdHistogramSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceScaleId: 'macd',
    })

    macdSeriesRef.current = macdSeries
    macdSignalSeriesRef.current = macdSignalSeries
    macdHistogramSeriesRef.current = macdHistogramSeries

    // Configure MACD price scale
    chart.priceScale('macd').applyOptions({
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    })

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    // Load data
    loadChartData(symbol)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
    }
  }, [symbol, height])

  const loadChartData = async (symbol: string) => {
    try {
      console.log(`🔄 Loading chart data for ${symbol}`)
      setIsLoading(true)

      // Fetch candlestick data
      const response = await fetch(`/api/stocks/${symbol}/candlestick?timeframe=1D&limit=500`)
      console.log('📡 API Response status:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('📊 API Result:', result)

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch chart data')
      }

      const chartData = result.data
      console.log('📈 Chart data loaded:', chartData?.length || 0, 'candles')
      console.log('📋 Sample data:', chartData?.slice(0, 2))

      if (!chartData || chartData.length === 0) {
        console.warn('⚠️ No chart data available')
        setIsLoading(false)
        return
      }

      // Format data for TradingView
      const candlestickData = chartData.map((item: any, index: number) => {
        const formatted = {
          time: item.date || item.time,
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
        }

        if (index < 3) {
          console.log(`🕯️ Candlestick ${index}:`, formatted)
        }

        return formatted
      })

      const volumeData = chartData.map((item: any) => ({
        time: item.date || item.time,
        value: parseFloat(item.volume || 0),
        color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a80' : '#ef535080',
      }))

      console.log('📊 Formatted candlestick data:', candlestickData.length, 'items')
      console.log('📊 Formatted volume data:', volumeData.length, 'items')

      // Calculate MACD (simplified)
      const macdData = calculateMACD(candlestickData)

      // Set data to series
      console.log('🎯 Setting data to chart series...')

      if (candlestickSeriesRef.current && candlestickData.length > 0) {
        console.log('📊 Setting candlestick data:', candlestickData.length, 'items')
        candlestickSeriesRef.current.setData(candlestickData)
        console.log('✅ Candlestick data set successfully')
      } else {
        console.error('❌ Candlestick series ref or data not available')
      }

      if (volumeSeriesRef.current && volumeData.length > 0) {
        console.log('📊 Setting volume data:', volumeData.length, 'items')
        volumeSeriesRef.current.setData(volumeData)
        console.log('✅ Volume data set successfully')
      }

      if (macdSeriesRef.current && macdData.macd.length > 0) {
        console.log('📊 Setting MACD data:', macdData.macd.length, 'items')
        macdSeriesRef.current.setData(macdData.macd)
      }

      if (macdSignalSeriesRef.current && macdData.signal.length > 0) {
        macdSignalSeriesRef.current.setData(macdData.signal)
      }

      if (macdHistogramSeriesRef.current && macdData.histogram.length > 0) {
        macdHistogramSeriesRef.current.setData(macdData.histogram)
      }

      // Store stock data for info display
      const latestData = chartData[chartData.length - 1]
      setStockData({
        symbol,
        price: parseFloat(latestData.close),
        change: parseFloat(latestData.close) - parseFloat(latestData.open),
        changePercent: ((parseFloat(latestData.close) - parseFloat(latestData.open)) / parseFloat(latestData.open)) * 100,
        volume: parseFloat(latestData.volume || 0),
        high: parseFloat(latestData.high),
        low: parseFloat(latestData.low),
      })

      // Detect candlestick patterns
      console.log('🔍 Detecting candlestick patterns...')
      const detectedPatterns = detectCandlestickPatterns(candlestickData)
      console.log('📊 Detected patterns:', detectedPatterns.length)

      // Convert patterns to chart positions
      const chartPatterns = detectedPatterns.map(pattern => {
        const candleIndex = pattern.candleIndex
        const xPosition = (candleIndex / candlestickData.length) * (chartContainerRef.current?.clientWidth || 800)
        const yPosition = 50 + Math.random() * 100 // Random Y position for now

        return {
          ...pattern,
          position: { x: xPosition, y: yPosition }
        }
      })

      setPatterns(chartPatterns)
      console.log('✅ Patterns set:', chartPatterns.length)

      // Update chart dimensions
      if (chartContainerRef.current) {
        setChartDimensions({
          width: chartContainerRef.current.clientWidth,
          height: height
        })
      }

      setIsLoading(false)

    } catch (error) {
      console.error('❌ Error loading chart data:', error)
      setIsLoading(false)
    }
  }

  // Simple MACD calculation
  const calculateMACD = (data: any[]) => {
    const closes = data.map(d => d.close)
    const ema12 = calculateEMA(closes, 12)
    const ema26 = calculateEMA(closes, 26)

    const macdLine = ema12.map((val, i) => val - ema26[i])
    const signalLine = calculateEMA(macdLine, 9)
    const histogram = macdLine.map((val, i) => val - signalLine[i])

    return {
      macd: macdLine.map((val, i) => ({
        time: data[i].time,
        value: val,
      })).filter(d => !isNaN(d.value)),
      signal: signalLine.map((val, i) => ({
        time: data[i].time,
        value: val,
      })).filter(d => !isNaN(d.value)),
      histogram: histogram.map((val, i) => ({
        time: data[i].time,
        value: val,
        color: val >= 0 ? '#26a69a80' : '#ef535080',
      })).filter(d => !isNaN(d.value)),
    }
  }

  // Simple EMA calculation
  const calculateEMA = (data: number[], period: number) => {
    const k = 2 / (period + 1)
    const ema = [data[0]]

    for (let i = 1; i < data.length; i++) {
      ema[i] = data[i] * k + ema[i - 1] * (1 - k)
    }

    return ema
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading chart data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full bg-gray-900" style={{ height: `${height}px` }}>
      {/* Stock Info Header */}
      {stockData && (
        <div className="absolute top-4 left-4 z-10 bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 text-white">
          <div className="flex items-center space-x-4">
            <div>
              <h3 className="text-lg font-bold">{stockData.symbol}</h3>
              <p className="text-sm text-gray-400">Himalayan Reinsurance Limited</p>
            </div>
            <div>
              <p className="text-xl font-bold">{stockData.price.toFixed(2)}</p>
              <p className={`text-sm ${stockData.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {stockData.change >= 0 ? '+' : ''}{stockData.change.toFixed(2)} ({stockData.changePercent.toFixed(2)}%)
              </p>
            </div>
            <div className="text-sm text-gray-400">
              <p>Volume: {stockData.volume.toLocaleString()}</p>
              <p>H: {stockData.high.toFixed(2)} L: {stockData.low.toFixed(2)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Chart Container */}
      <div
        ref={chartContainerRef}
        className="w-full h-full relative"
        style={{ height: `${height}px` }}
      >
        {/* Pattern Chips Overlay */}
        {!isLoading && patterns.length > 0 && (
          <PatternChips
            patterns={patterns}
            chartWidth={chartDimensions.width}
            chartHeight={chartDimensions.height}
            onPatternClick={(pattern) => {
              console.log('Pattern clicked:', pattern)
              // You can add pattern details modal here
            }}
          />
        )}
      </div>
    </div>
  )
}

export default ProfessionalChart
