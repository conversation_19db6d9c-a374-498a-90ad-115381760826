import React, { useState } from 'react'
import { Search, TrendingUp, BarChart3, Volume2, Settings } from 'lucide-react'
import { useChartStore } from '@/stores/chartStore'
import { TimeFrame } from '@/types'
import { TIMEFRAMES } from '@/constants'

interface ChartControlsProps {
  onSymbolChange: (symbol: string) => void
  currentSymbol: string
}

const ChartControls: React.FC<ChartControlsProps> = ({ onSymbolChange, currentSymbol }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isSearching, setIsSearching] = useState(false)

  const { timeframe, setTimeframe } = useChartStore()

  const handleSearch = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([])
      setShowSearchResults(false)
      return
    }

    setIsSearching(true)
    try {
      const response = await fetch(`/api/search/stocks?q=${encodeURIComponent(query)}`)
      const result = await response.json()
      
      if (result.success) {
        setSearchResults(result.data)
        setShowSearchResults(true)
      }
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleSymbolSelect = (symbol: string) => {
    onSymbolChange(symbol)
    setSearchQuery(symbol)
    setShowSearchResults(false)
  }

  const handleTimeframeChange = (newTimeframe: TimeFrame) => {
    setTimeframe(newTimeframe)
  }

  return (
    <div className="bg-slate-800 border-b border-slate-700 p-4">
      <div className="flex items-center justify-between">
        {/* Left side - Symbol search and info */}
        <div className="flex items-center space-x-4">
          {/* Symbol Search */}
          <div className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search stocks..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value)
                  handleSearch(e.target.value)
                }}
                className="input-primary pl-10 w-64"
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="loading-spinner w-4 h-4"></div>
                </div>
              )}
            </div>

            {/* Search Results Dropdown */}
            {showSearchResults && searchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                {searchResults.map((company) => (
                  <button
                    key={company.company_id}
                    onClick={() => handleSymbolSelect(company.symbol)}
                    className="w-full text-left px-4 py-2 hover:bg-slate-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-white">{company.symbol}</div>
                        <div className="text-sm text-slate-400 truncate">{company.name}</div>
                      </div>
                      <TrendingUp className="h-4 w-4 text-slate-400" />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Current Symbol Info */}
          {currentSymbol && (
            <div className="flex items-center space-x-2">
              <div className="bg-blue-500/20 px-3 py-1 rounded-md">
                <span className="text-blue-400 font-medium">{currentSymbol}</span>
              </div>
            </div>
          )}
        </div>

        {/* Center - Timeframe Selection */}
        <div className="flex items-center space-x-1 bg-slate-700 rounded-lg p-1">
          {TIMEFRAMES.map((tf) => (
            <button
              key={tf.value}
              onClick={() => handleTimeframeChange(tf.value)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeframe === tf.value
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-300 hover:text-white hover:bg-slate-600'
              }`}
              title={tf.description}
            >
              {tf.label}
            </button>
          ))}
        </div>

        {/* Right side - Chart options */}
        <div className="flex items-center space-x-2">
          {/* Chart Type */}
          <div className="flex items-center space-x-1 bg-slate-700 rounded-lg p-1">
            <button
              className="p-2 rounded-md text-slate-300 hover:text-white hover:bg-slate-600 transition-colors"
              title="Candlestick Chart"
            >
              <BarChart3 className="h-4 w-4" />
            </button>
            <button
              className="p-2 rounded-md text-slate-300 hover:text-white hover:bg-slate-600 transition-colors"
              title="Volume"
            >
              <Volume2 className="h-4 w-4" />
            </button>
          </div>

          {/* Settings */}
          <button
            className="p-2 rounded-md text-slate-300 hover:text-white hover:bg-slate-600 transition-colors"
            title="Chart Settings"
          >
            <Settings className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Click outside to close search results */}
      {showSearchResults && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowSearchResults(false)}
        />
      )}
    </div>
  )
}

export default ChartControls
