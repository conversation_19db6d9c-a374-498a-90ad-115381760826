import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TrendingUp, 
  Activity, 
  BarChart3, 
  Plus, 
  X, 
  Eye,
  EyeOff,
  Settings
} from 'lucide-react'

interface Indicator {
  id: string
  name: string
  type: 'trend' | 'momentum' | 'volatility' | 'volume'
  enabled: boolean
  color: string
  parameters?: Record<string, any>
}

const IndicatorsPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [activeIndicators, setActiveIndicators] = useState<Indicator[]>([
    {
      id: 'sma_20',
      name: 'SMA (20)',
      type: 'trend',
      enabled: true,
      color: '#3b82f6',
      parameters: { period: 20 }
    },
    {
      id: 'rsi_14',
      name: 'RSI (14)',
      type: 'momentum',
      enabled: true,
      color: '#8b5cf6',
      parameters: { period: 14 }
    }
  ])

  const availableIndicators = [
    // Trend Indicators
    { id: 'sma', name: 'Simple Moving Average', type: 'trend', category: 'Trend' },
    { id: 'ema', name: 'Exponential Moving Average', type: 'trend', category: 'Trend' },
    { id: 'wma', name: 'Weighted Moving Average', type: 'trend', category: 'Trend' },
    { id: 'bollinger', name: 'Bollinger Bands', type: 'volatility', category: 'Volatility' },
    
    // Momentum Indicators
    { id: 'rsi', name: 'Relative Strength Index', type: 'momentum', category: 'Momentum' },
    { id: 'macd', name: 'MACD', type: 'momentum', category: 'Momentum' },
    { id: 'stochastic', name: 'Stochastic Oscillator', type: 'momentum', category: 'Momentum' },
    
    // Volume Indicators
    { id: 'vwap', name: 'VWAP', type: 'volume', category: 'Volume' },
    { id: 'obv', name: 'On Balance Volume', type: 'volume', category: 'Volume' },
  ]

  const toggleIndicator = (indicatorId: string) => {
    setActiveIndicators(prev =>
      prev.map(indicator =>
        indicator.id === indicatorId
          ? { ...indicator, enabled: !indicator.enabled }
          : indicator
      )
    )
  }

  const removeIndicator = (indicatorId: string) => {
    setActiveIndicators(prev => prev.filter(indicator => indicator.id !== indicatorId))
  }

  const addIndicator = (indicatorType: string) => {
    const newIndicator: Indicator = {
      id: `${indicatorType}_${Date.now()}`,
      name: availableIndicators.find(i => i.id === indicatorType)?.name || indicatorType,
      type: availableIndicators.find(i => i.id === indicatorType)?.type as any || 'trend',
      enabled: true,
      color: '#f59e0b',
      parameters: { period: 14 }
    }
    
    setActiveIndicators(prev => [...prev, newIndicator])
  }

  const getIndicatorIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <TrendingUp className="h-4 w-4" />
      case 'momentum':
        return <Activity className="h-4 w-4" />
      case 'volatility':
        return <BarChart3 className="h-4 w-4" />
      case 'volume':
        return <BarChart3 className="h-4 w-4" />
      default:
        return <TrendingUp className="h-4 w-4" />
    }
  }

  const groupedIndicators = availableIndicators.reduce((acc, indicator) => {
    const category = indicator.category
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(indicator)
    return acc
  }, {} as Record<string, typeof availableIndicators>)

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 z-40 bg-slate-800 border border-slate-600 rounded-l-lg p-3 hover:bg-slate-700 transition-colors"
        title="Technical Indicators"
      >
        <BarChart3 className="h-5 w-5 text-slate-300" />
      </button>

      {/* Indicators Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/20 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 bottom-0 w-80 bg-slate-800 border-l border-slate-700 z-50 overflow-hidden flex flex-col"
            >
              {/* Header */}
              <div className="p-4 border-b border-slate-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">Technical Indicators</h3>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-1 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {/* Active Indicators */}
                <div className="p-4">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Active Indicators</h4>
                  <div className="space-y-2">
                    {activeIndicators.map((indicator) => (
                      <div
                        key={indicator.id}
                        className="flex items-center justify-between p-3 bg-slate-700 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: indicator.color }}
                          />
                          <div>
                            <div className="text-white text-sm font-medium">{indicator.name}</div>
                            <div className="text-slate-400 text-xs capitalize">{indicator.type}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => toggleIndicator(indicator.id)}
                            className="p-1 rounded hover:bg-slate-600 transition-colors"
                            title={indicator.enabled ? 'Hide' : 'Show'}
                          >
                            {indicator.enabled ? (
                              <Eye className="h-4 w-4 text-slate-300" />
                            ) : (
                              <EyeOff className="h-4 w-4 text-slate-500" />
                            )}
                          </button>
                          <button
                            className="p-1 rounded hover:bg-slate-600 transition-colors"
                            title="Settings"
                          >
                            <Settings className="h-4 w-4 text-slate-400" />
                          </button>
                          <button
                            onClick={() => removeIndicator(indicator.id)}
                            className="p-1 rounded hover:bg-slate-600 transition-colors"
                            title="Remove"
                          >
                            <X className="h-4 w-4 text-slate-400" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Add Indicators */}
                <div className="p-4 border-t border-slate-700">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Add Indicators</h4>
                  <div className="space-y-4">
                    {Object.entries(groupedIndicators).map(([category, indicators]) => (
                      <div key={category}>
                        <h5 className="text-xs font-medium text-slate-400 mb-2 uppercase tracking-wide">
                          {category}
                        </h5>
                        <div className="space-y-1">
                          {indicators.map((indicator) => (
                            <button
                              key={indicator.id}
                              onClick={() => addIndicator(indicator.id)}
                              className="w-full flex items-center justify-between p-2 rounded-md hover:bg-slate-700 transition-colors text-left"
                            >
                              <div className="flex items-center space-x-2">
                                {getIndicatorIcon(indicator.type)}
                                <span className="text-slate-300 text-sm">{indicator.name}</span>
                              </div>
                              <Plus className="h-4 w-4 text-slate-400" />
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

export default IndicatorsPanel
