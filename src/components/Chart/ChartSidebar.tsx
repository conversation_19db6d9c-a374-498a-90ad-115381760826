import React, { useState } from 'react'
import { 
  TrendingUp, 
  Activity, 
  BarChart3, 
  Volume2,
  Eye,
  EyeOff,
  Settings,
  Plus,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react'

interface Indicator {
  id: string
  name: string
  type: 'trend' | 'momentum' | 'volatility' | 'volume'
  enabled: boolean
  color: string
  value?: number
  change?: number
}

const ChartSidebar: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'indicators' | 'watchlist' | 'trades'>('indicators')
  const [expandedSections, setExpandedSections] = useState<string[]>(['trend', 'momentum'])
  
  const [indicators, setIndicators] = useState<Indicator[]>([
    { id: 'sma20', name: 'SMA (20)', type: 'trend', enabled: true, color: '#2196F3', value: 678.81 },
    { id: 'sma50', name: 'SMA (50)', type: 'trend', enabled: true, color: '#FF9800', value: 687.79 },
    { id: 'rsi', name: 'RSI (14)', type: 'momentum', enabled: true, color: '#9C27B0', value: 52.3 },
    { id: 'macd', name: 'MACD', type: 'momentum', enabled: true, color: '#4CAF50', value: 12.26, change: -3.42 },
    { id: 'bb', name: 'Bollinger Bands', type: 'volatility', enabled: false, color: '#FF5722' },
    { id: 'volume', name: 'Volume', type: 'volume', enabled: true, color: '#607D8B', value: 13067 },
  ])

  const availableIndicators = {
    trend: [
      { id: 'sma', name: 'Simple Moving Average' },
      { id: 'ema', name: 'Exponential Moving Average' },
      { id: 'wma', name: 'Weighted Moving Average' },
      { id: 'vwap', name: 'VWAP' },
    ],
    momentum: [
      { id: 'rsi', name: 'RSI' },
      { id: 'macd', name: 'MACD' },
      { id: 'stoch', name: 'Stochastic' },
      { id: 'cci', name: 'CCI' },
    ],
    volatility: [
      { id: 'bb', name: 'Bollinger Bands' },
      { id: 'atr', name: 'ATR' },
      { id: 'kc', name: 'Keltner Channels' },
    ],
    volume: [
      { id: 'volume', name: 'Volume' },
      { id: 'obv', name: 'On Balance Volume' },
      { id: 'ad', name: 'Accumulation/Distribution' },
    ],
  }

  const watchlist = [
    { symbol: 'NABIL', price: 1200.00, change: 2.5, changePercent: 0.21 },
    { symbol: 'NICA', price: 850.00, change: -5.0, changePercent: -0.58 },
    { symbol: 'EBL', price: 650.00, change: 10.0, changePercent: 1.56 },
    { symbol: 'KBL', price: 280.00, change: -2.0, changePercent: -0.71 },
    { symbol: 'HRL', price: 947.00, change: 15.0, changePercent: 1.61 },
  ]

  const toggleIndicator = (id: string) => {
    setIndicators(prev =>
      prev.map(indicator =>
        indicator.id === id
          ? { ...indicator, enabled: !indicator.enabled }
          : indicator
      )
    )
  }

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const getIndicatorIcon = (type: string) => {
    switch (type) {
      case 'trend': return <TrendingUp className="h-4 w-4" />
      case 'momentum': return <Activity className="h-4 w-4" />
      case 'volatility': return <BarChart3 className="h-4 w-4" />
      case 'volume': return <Volume2 className="h-4 w-4" />
      default: return <TrendingUp className="h-4 w-4" />
    }
  }

  return (
    <div className="w-80 bg-gray-900 border-l border-gray-700 flex flex-col h-full">
      {/* Tabs */}
      <div className="flex border-b border-gray-700">
        {[
          { id: 'indicators', label: 'Indicators' },
          { id: 'watchlist', label: 'Watchlist' },
          { id: 'trades', label: 'Trades' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'text-white border-b-2 border-blue-500 bg-gray-800'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'indicators' && (
          <div className="p-4">
            {/* Active Indicators */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-300 mb-3">Active Indicators</h3>
              <div className="space-y-2">
                {indicators.filter(i => i.enabled).map((indicator) => (
                  <div
                    key={indicator.id}
                    className="flex items-center justify-between p-3 bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: indicator.color }}
                      />
                      <div>
                        <div className="text-white text-sm font-medium">{indicator.name}</div>
                        {indicator.value && (
                          <div className="text-gray-400 text-xs">
                            {indicator.value.toFixed(2)}
                            {indicator.change && (
                              <span className={`ml-2 ${indicator.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {indicator.change >= 0 ? '+' : ''}{indicator.change.toFixed(2)}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => toggleIndicator(indicator.id)}
                        className="p-1 rounded hover:bg-gray-700 transition-colors"
                      >
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      </button>
                      <button className="p-1 rounded hover:bg-gray-700 transition-colors">
                        <Settings className="h-4 w-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Add Indicators */}
            <div>
              <h3 className="text-sm font-medium text-gray-300 mb-3">Add Indicators</h3>
              <div className="space-y-2">
                {Object.entries(availableIndicators).map(([category, items]) => (
                  <div key={category}>
                    <button
                      onClick={() => toggleSection(category)}
                      className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-800 rounded-lg transition-colors"
                    >
                      <div className="flex items-center space-x-2">
                        {getIndicatorIcon(category)}
                        <span className="text-gray-300 text-sm capitalize">{category}</span>
                      </div>
                      {expandedSections.includes(category) ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                    
                    {expandedSections.includes(category) && (
                      <div className="ml-6 space-y-1">
                        {items.map((item) => (
                          <button
                            key={item.id}
                            className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-800 rounded-lg transition-colors"
                          >
                            <span className="text-gray-400 text-sm">{item.name}</span>
                            <Plus className="h-4 w-4 text-gray-400" />
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'watchlist' && (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-300">Watchlist</h3>
              <button className="p-1 rounded hover:bg-gray-800 transition-colors">
                <Plus className="h-4 w-4 text-gray-400" />
              </button>
            </div>
            <div className="space-y-2">
              {watchlist.map((stock) => (
                <div
                  key={stock.symbol}
                  className="p-3 bg-gray-800 rounded-lg hover:bg-gray-750 transition-colors cursor-pointer"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">{stock.symbol}</div>
                      <div className="text-gray-400 text-sm">{stock.price.toFixed(2)}</div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        stock.change >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)}
                      </div>
                      <div className={`text-xs ${
                        stock.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'trades' && (
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-300 mb-4">Recent Trades</h3>
            <div className="text-center text-gray-500 py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No trades yet</p>
              <p className="text-xs mt-1">Your trading activity will appear here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChartSidebar
