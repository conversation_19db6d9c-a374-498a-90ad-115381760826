import React, { useEffect, useRef } from 'react'
import { createChart, IChartApi } from 'lightweight-charts'

interface SimpleChartProps {
  symbol: string
  height?: number
}

const SimpleChart: React.FC<SimpleChartProps> = ({ symbol, height = 400 }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)

  useEffect(() => {
    if (!chartContainerRef.current) return

    console.log('🚀 Creating simple chart for', symbol)

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth || 800,
      height: height - 80,
      layout: {
        background: { color: '#0f172a' },
        textColor: '#e2e8f0',
      },
      grid: {
        vertLines: { color: '#334155' },
        horzLines: { color: '#334155' },
      },
      rightPriceScale: {
        borderColor: '#334155',
      },
      timeScale: {
        borderColor: '#334155',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderUpColor: '#10b981',
      borderDownColor: '#ef4444',
      wickUpColor: '#10b981',
      wickDownColor: '#ef4444',
    })

    console.log('📊 Chart created, loading real data...')

    // Load real data
    const loadRealData = async () => {
      try {
        const response = await fetch(`/api/stocks/${symbol}/candlestick?timeframe=1D&limit=100`)
        console.log('📡 API Response status:', response.status)

        if (response.ok) {
          const result = await response.json()
          console.log('📊 API Result:', result)

          if (result.success && result.data && result.data.length > 0) {
            const formattedData = result.data.map((item: any) => {
              // Convert ISO date to YYYY-MM-DD format for TradingView
              const date = new Date(item.time || item.date)
              const timeString = date.toISOString().split('T')[0] // Get YYYY-MM-DD

              return {
                time: timeString,
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
              }
            }).reverse() // Reverse to get chronological order (oldest first)

            console.log('✅ Setting real data:', formattedData.length, 'candles')
            console.log('📊 Sample formatted data:', formattedData.slice(0, 3))
            candlestickSeries.setData(formattedData)
            return
          }
        }

        console.log('⚠️ Using sample data as fallback')
        // Fallback to sample data
        const sampleData = [
          { time: '2024-01-01', open: 100, high: 110, low: 95, close: 105 },
          { time: '2024-01-02', open: 105, high: 115, low: 100, close: 112 },
          { time: '2024-01-03', open: 112, high: 118, low: 108, close: 115 },
          { time: '2024-01-04', open: 115, high: 120, low: 110, close: 108 },
          { time: '2024-01-05', open: 108, high: 113, low: 105, close: 111 },
        ]
        candlestickSeries.setData(sampleData)

      } catch (error) {
        console.error('❌ Error loading data:', error)
        // Use sample data on error
        const sampleData = [
          { time: '2024-01-01', open: 100, high: 110, low: 95, close: 105 },
          { time: '2024-01-02', open: 105, high: 115, low: 100, close: 112 },
          { time: '2024-01-03', open: 112, high: 118, low: 108, close: 115 },
          { time: '2024-01-04', open: 115, high: 120, low: 110, close: 108 },
          { time: '2024-01-05', open: 108, high: 113, low: 105, close: 111 },
        ]
        candlestickSeries.setData(sampleData)
      }
    }

    loadRealData()

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
    }
  }, [symbol, height])

  return (
    <div className="w-full" style={{ height: `${height}px` }}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-white">Simple Chart Test - {symbol}</h3>
        <p className="text-slate-400 text-sm">Testing TradingView Lightweight Charts integration</p>
      </div>
      <div
        ref={chartContainerRef}
        className="w-full bg-slate-900 border border-slate-700 rounded-lg"
        style={{
          height: `${height - 80}px`,
          minHeight: `${height - 80}px`,
          width: '100%'
        }}
      />
    </div>
  )
}

export default SimpleChart
