import React, { useState } from 'react'
import { 
  Search, 
  TrendingUp, 
  BarChart3, 
  Volume2, 
  Settings,
  Maximize2,
  Download,
  Share2,
  Bell,
  Bookmark,
  MoreHorizontal
} from 'lucide-react'

interface ChartToolbarProps {
  symbol: string
  onSymbolChange: (symbol: string) => void
  timeframe: string
  onTimeframeChange: (timeframe: string) => void
}

const ChartToolbar: React.FC<ChartToolbarProps> = ({
  symbol,
  onSymbolChange,
  timeframe,
  onTimeframeChange
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)

  const timeframes = [
    { label: '5y', value: '5Y', description: '5 Years' },
    { label: '1y', value: '1Y', description: '1 Year' },
    { label: '6m', value: '6M', description: '6 Months' },
    { label: '3m', value: '3M', description: '3 Months' },
    { label: '1m', value: '1M', description: '1 Month' },
    { label: '5d', value: '5D', description: '5 Days' },
    { label: '1d', value: '1D', description: '1 Day' },
  ]

  const chartTypes = [
    { icon: BarChart3, label: 'Candlestick', active: true },
    { icon: TrendingUp, label: 'Line', active: false },
    { icon: Volume2, label: 'Volume', active: false },
  ]

  return (
    <div className="bg-gray-900 border-b border-gray-700 px-4 py-2">
      <div className="flex items-center justify-between">
        {/* Left Section - Symbol and Search */}
        <div className="flex items-center space-x-4">
          {/* Symbol Display */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors"
            >
              <Search className="h-4 w-4 text-gray-400" />
              <span className="text-white font-medium">{symbol}</span>
            </button>
            
            {/* Quick Symbol Buttons */}
            <div className="flex items-center space-x-1">
              {['NABIL', 'NICA', 'EBL', 'KBL', 'HRL'].map((sym) => (
                <button
                  key={sym}
                  onClick={() => onSymbolChange(sym)}
                  className={`px-2 py-1 text-xs rounded transition-colors ${
                    symbol === sym
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  {sym}
                </button>
              ))}
            </div>
          </div>

          {/* Chart Type Selector */}
          <div className="flex items-center space-x-1 bg-gray-800 rounded-lg p-1">
            {chartTypes.map((type, index) => (
              <button
                key={index}
                className={`p-2 rounded-md transition-colors ${
                  type.active
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
                title={type.label}
              >
                <type.icon className="h-4 w-4" />
              </button>
            ))}
          </div>
        </div>

        {/* Center Section - Timeframe Selector */}
        <div className="flex items-center space-x-1 bg-gray-800 rounded-lg p-1">
          {timeframes.map((tf) => (
            <button
              key={tf.value}
              onClick={() => onTimeframeChange(tf.value)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                timeframe === tf.value
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              title={tf.description}
            >
              {tf.label}
            </button>
          ))}
        </div>

        {/* Right Section - Tools */}
        <div className="flex items-center space-x-2">
          {/* Drawing Tools */}
          <div className="flex items-center space-x-1 bg-gray-800 rounded-lg p-1">
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Trend Line">
              <TrendingUp className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Rectangle">
              <div className="w-4 h-4 border border-current rounded-sm" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Fibonacci">
              <div className="w-4 h-4 text-xs font-bold">φ</div>
            </button>
          </div>

          {/* Indicators */}
          <button className="px-3 py-2 bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white rounded-lg transition-colors text-sm">
            Indicators
          </button>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Alerts">
              <Bell className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Save">
              <Bookmark className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Share">
              <Share2 className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Download">
              <Download className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Fullscreen">
              <Maximize2 className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="Settings">
              <Settings className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" title="More">
              <MoreHorizontal className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Search Dropdown */}
      {showSearch && (
        <div className="absolute top-full left-4 right-4 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 max-h-64 overflow-y-auto">
          <div className="p-3">
            <input
              type="text"
              placeholder="Search stocks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-700 text-white placeholder-gray-400 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              autoFocus
            />
          </div>
          <div className="border-t border-gray-700">
            {/* Sample search results */}
            {['NABIL', 'NICA', 'EBL', 'KBL', 'HRL', 'ADBL', 'GBIME', 'MBL', 'NBL', 'SBL'].map((sym) => (
              <button
                key={sym}
                onClick={() => {
                  onSymbolChange(sym)
                  setShowSearch(false)
                  setSearchQuery('')
                }}
                className="w-full text-left px-4 py-2 hover:bg-gray-700 transition-colors flex items-center justify-between"
              >
                <div>
                  <div className="text-white font-medium">{sym}</div>
                  <div className="text-gray-400 text-sm">Nepal Stock Exchange</div>
                </div>
                <TrendingUp className="h-4 w-4 text-gray-400" />
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Click outside to close search */}
      {showSearch && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowSearch(false)}
        />
      )}
    </div>
  )
}

export default ChartToolbar
