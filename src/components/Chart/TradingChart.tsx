import React, { useEffect, useRef, useState } from 'react'
import { create<PERSON><PERSON>, IChartApi, ISeriesApi, Time } from 'lightweight-charts'
import { useChartStore } from '@/stores/chartStore'
import { CandlestickData as AppCandlestickData } from '@/types'
import { CHART_CONFIG, COLORS } from '@/constants'
import { patternDetectionService } from '@/services/patternDetection'
import PatternOverlay from './PatternOverlay'

interface TradingChartProps {
  symbol: string
  height?: number
  showVolume?: boolean
}

const TradingChart: React.FC<TradingChartProps> = ({
  symbol,
  height = CHART_CONFIG.DEFAULT_HEIGHT,
  showVolume = true
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [minConfidence, setMinConfidence] = useState(0.7)

  const {
    candlestickData,
    volumeData,
    timeframe,
    patterns,
    setCandlestickData,
    setVolumeData,
    setLoading,
    setError: setStoreError,
    addPattern,
    removePattern
  } = useChartStore()

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) {
      console.log('Chart container ref not available')
      return
    }

    console.log('Initializing chart with container:', chartContainerRef.current)
    console.log('Container dimensions:', chartContainerRef.current.clientWidth, 'x', height)

    // Create chart instance
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth || 800,
      height: height,
      layout: {
        background: { color: '#0f172a' },
        textColor: '#e2e8f0',
      },
      grid: {
        vertLines: { color: '#334155' },
        horzLines: { color: '#334155' },
      },
      crosshair: {
        mode: 1, // Normal crosshair mode
        vertLine: {
          color: '#64748b',
          width: 1,
          style: 2, // Dashed line
        },
        horzLine: {
          color: '#64748b',
          width: 1,
          style: 2, // Dashed line
        },
      },
      rightPriceScale: {
        borderColor: '#334155',
      },
      timeScale: {
        borderColor: '#334155',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    console.log('Chart created successfully:', chart)

    chartRef.current = chart

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderUpColor: '#10b981',
      borderDownColor: '#ef4444',
      wickUpColor: '#10b981',
      wickDownColor: '#ef4444',
    })

    console.log('Candlestick series created:', candlestickSeries)
    candlestickSeriesRef.current = candlestickSeries

    // Create volume series if enabled
    if (showVolume) {
      const volumeSeries = chart.addHistogramSeries({
        color: '#6b7280',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: 'volume',
      })

      chart.priceScale('volume').applyOptions({
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      })

      console.log('Volume series created:', volumeSeries)
      volumeSeriesRef.current = volumeSeries
    }

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
    }
  }, [height, showVolume])

  // Load data when symbol or timeframe changes
  useEffect(() => {
    if (symbol) {
      loadChartData(symbol, timeframe)
    }
  }, [symbol, timeframe])

  // Update chart data when store data changes
  useEffect(() => {
    console.log('Candlestick data updated:', candlestickData.length, 'items')
    console.log('Candlestick series ref:', candlestickSeriesRef.current)

    if (candlestickSeriesRef.current && candlestickData.length > 0) {
      const formattedData = candlestickData.map(item => ({
        time: item.time as Time,
        open: Number(item.open),
        high: Number(item.high),
        low: Number(item.low),
        close: Number(item.close),
      }))

      console.log('Setting candlestick data:', formattedData.slice(0, 3)) // Log first 3 items
      candlestickSeriesRef.current.setData(formattedData)
      setIsLoading(false)
    }
  }, [candlestickData])

  // Update volume data
  useEffect(() => {
    if (volumeSeriesRef.current && volumeData.length > 0) {
      const formattedVolumeData = volumeData.map(item => ({
        time: item.time as Time,
        value: item.value,
        color: item.value > 0 ? '#6b7280' : '#4b5563',
      }))

      volumeSeriesRef.current.setData(formattedVolumeData)
    }
  }, [volumeData])

  const loadChartData = async (symbol: string, timeframe: string) => {
    try {
      console.log(`Loading chart data for ${symbol} (${timeframe})`)
      setIsLoading(true)
      setError(null)
      setStoreError(null)
      setLoading(true)

      // Fetch candlestick data
      const response = await fetch(`/api/stocks/${symbol}/candlestick?timeframe=${timeframe}&limit=500`)

      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('API response:', result)

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch chart data')
      }

      const chartData: AppCandlestickData[] = result.data
      console.log('Chart data received:', chartData.length, 'items')

      // Transform data for chart
      setCandlestickData(chartData)

      // Create volume data from candlestick data
      const volumeData = chartData.map(item => ({
        time: item.time,
        value: item.volume || 0,
      }))

      setVolumeData(volumeData)

      // Detect patterns
      patternDetectionService.setMinConfidence(minConfidence)
      const detectedPatterns = patternDetectionService.detectPatterns(chartData)

      // Clear existing patterns and add new ones
      patterns.forEach(pattern => removePattern(pattern.pattern.id))
      detectedPatterns.forEach(pattern => addPattern(pattern))

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load chart data'
      setError(errorMessage)
      setStoreError(errorMessage)
      console.error('Chart data loading error:', err)
    } finally {
      setIsLoading(false)
      setLoading(false)
    }
  }

  const handlePatternToggle = (patternId: string, enabled: boolean) => {
    // This will be used to show/hide patterns on the chart
    console.log(`Pattern ${patternId} ${enabled ? 'enabled' : 'disabled'}`)
  }

  const handleConfidenceChange = (confidence: number) => {
    setMinConfidence(confidence)
    if (candlestickData.length > 0) {
      // Re-detect patterns with new confidence threshold
      patternDetectionService.setMinConfidence(confidence)
      const detectedPatterns = patternDetectionService.detectPatterns(candlestickData)

      // Clear existing patterns and add new ones
      patterns.forEach(pattern => removePattern(pattern.pattern.id))
      detectedPatterns.forEach(pattern => addPattern(pattern))
    }
  }

  if (error) {
    return (
      <div className="chart-container flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 mb-4">
            <svg className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-medium">Chart Error</p>
          </div>
          <p className="text-slate-400 mb-4">{error}</p>
          <button
            onClick={() => loadChartData(symbol, timeframe)}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="chart-container flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-slate-400">Loading chart data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="chart-container relative w-full" style={{ height: `${height}px` }}>
      <div
        ref={chartContainerRef}
        className="w-full h-full bg-slate-900"
        style={{
          height: `${height}px`,
          minHeight: `${height}px`,
          width: '100%'
        }}
      />

      {/* Pattern Overlay */}
      <PatternOverlay
        patterns={patterns}
        onPatternToggle={handlePatternToggle}
        onConfidenceChange={handleConfidenceChange}
        minConfidence={minConfidence}
      />
    </div>
  )
}

export default TradingChart
