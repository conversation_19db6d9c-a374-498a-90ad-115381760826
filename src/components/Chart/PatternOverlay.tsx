import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Activity, Eye, EyeOff, Info } from 'lucide-react'
import { PatternDetection } from '@/types'

interface PatternOverlayProps {
  patterns: PatternDetection[]
  onPatternToggle: (patternId: string, enabled: boolean) => void
  onConfidenceChange: (confidence: number) => void
  minConfidence: number
}

const PatternOverlay: React.FC<PatternOverlayProps> = ({
  patterns,
  onPatternToggle,
  onConfidenceChange,
  minConfidence
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedPattern, setSelectedPattern] = useState<PatternDetection | null>(null)
  const [enabledPatterns, setEnabledPatterns] = useState<Set<string>>(new Set())

  const handlePatternToggle = (patternId: string) => {
    const newEnabledPatterns = new Set(enabledPatterns)
    const isEnabled = !enabledPatterns.has(patternId)

    if (isEnabled) {
      newEnabledPatterns.add(patternId)
    } else {
      newEnabledPatterns.delete(patternId)
    }

    setEnabledPatterns(newEnabledPatterns)
    onPatternToggle(patternId, isEnabled)
  }

  const getPatternColor = (pattern: PatternDetection) => {
    if (pattern.pattern.category === 'reversal') {
      return pattern.pattern.bullish ? '#10b981' : '#ef4444'
    }
    return '#6b7280'
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400'
    if (confidence >= 0.6) return 'text-yellow-400'
    return 'text-red-400'
  }

  const groupedPatterns = patterns.reduce((acc, pattern) => {
    const type = pattern.pattern.type
    if (!acc[type]) acc[type] = []
    acc[type].push(pattern)
    return acc
  }, {} as Record<string, PatternDetection[]>)

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed left-4 top-1/2 transform -translate-y-1/2 z-40 bg-slate-800 border border-slate-600 rounded-r-lg p-3 hover:bg-slate-700 transition-colors"
        title="Candlestick Patterns"
      >
        <Activity className="h-5 w-5 text-slate-300" />
        {patterns.length > 0 && (
          <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {patterns.length}
          </span>
        )}
      </button>

      {/* Pattern Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/20 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Panel */}
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed left-0 top-0 bottom-0 w-80 bg-slate-800 border-r border-slate-700 z-50 overflow-hidden flex flex-col"
            >
              {/* Header */}
              <div className="p-4 border-b border-slate-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">Candlestick Patterns</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-slate-400">{patterns.length} found</span>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-1 rounded-md hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
                    >
                      <Activity className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Confidence Filter */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-slate-300">
                      Min Confidence
                    </label>
                    <span className="text-sm text-slate-400">
                      {(minConfidence * 100).toFixed(0)}%
                    </span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={minConfidence}
                    onChange={(e) => onConfidenceChange(Number(e.target.value))}
                    className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {patterns.length === 0 ? (
                  <div className="p-4 text-center">
                    <Activity className="h-12 w-12 text-slate-600 mx-auto mb-2" />
                    <p className="text-slate-400">No patterns detected</p>
                    <p className="text-slate-500 text-sm mt-1">
                      Try lowering the confidence threshold
                    </p>
                  </div>
                ) : (
                  <div className="p-4 space-y-4">
                    {Object.entries(groupedPatterns).map(([type, typePatterns]) => (
                      <div key={type}>
                        <h4 className="text-sm font-medium text-slate-300 mb-2 capitalize">
                          {type} Candlestick Patterns
                        </h4>
                        <div className="space-y-2">
                          {typePatterns.map((pattern, index) => (
                            <div
                              key={`${pattern.pattern.id}-${pattern.timestamp}-${index}`}
                              className="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer"
                              onClick={() => setSelectedPattern(pattern)}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: getPatternColor(pattern) }}
                                  />
                                  <span className="text-white font-medium text-sm">
                                    {pattern.pattern.name}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <span className={`text-xs font-medium ${getConfidenceColor(pattern.confidence)}`}>
                                    {(pattern.confidence * 100).toFixed(0)}%
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handlePatternToggle(pattern.pattern.id)
                                    }}
                                    className="p-1 rounded hover:bg-slate-500 transition-colors"
                                  >
                                    {enabledPatterns.has(pattern.pattern.id) ? (
                                      <Eye className="h-4 w-4 text-slate-300" />
                                    ) : (
                                      <EyeOff className="h-4 w-4 text-slate-500" />
                                    )}
                                  </button>
                                </div>
                              </div>

                              <div className="text-xs text-slate-400 mb-1">
                                {pattern.pattern.category} • {pattern.pattern.bullish ? 'Bullish' : 'Bearish'}
                              </div>

                              <div className="text-xs text-slate-400">
                                Time: {new Date(pattern.timestamp).toLocaleString()}
                              </div>

                              <div className="text-xs text-slate-300 mt-1">
                                {pattern.description}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Pattern Detail Modal */}
      <AnimatePresence>
        {selectedPattern && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-60 flex items-center justify-center p-4"
              onClick={() => setSelectedPattern(null)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-slate-800 rounded-lg p-6 max-w-md w-full border border-slate-600"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">
                    {selectedPattern.pattern.name}
                  </h3>
                  <button
                    onClick={() => setSelectedPattern(null)}
                    className="text-slate-400 hover:text-white"
                  >
                    <Info className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-slate-400">Type:</span>
                    <span className="text-white ml-2 capitalize">{selectedPattern.pattern.type}</span>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Category:</span>
                    <span className="text-white ml-2 capitalize">{selectedPattern.pattern.category}</span>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Direction:</span>
                    <span className={`ml-2 ${selectedPattern.pattern.bullish ? 'text-green-400' : 'text-red-400'}`}>
                      {selectedPattern.pattern.bullish ? 'Bullish' : 'Bearish'}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Confidence:</span>
                    <span className={`ml-2 font-medium ${getConfidenceColor(selectedPattern.confidence)}`}>
                      {(selectedPattern.confidence * 100).toFixed(1)}%
                    </span>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Strength:</span>
                    <span className="text-white ml-2">{selectedPattern.pattern.strength}/10</span>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Description:</span>
                    <p className="text-slate-300 text-sm mt-1">{selectedPattern.pattern.description}</p>
                  </div>

                  <div>
                    <span className="text-sm text-slate-400">Detection Details:</span>
                    <p className="text-slate-300 text-sm mt-1">{selectedPattern.description}</p>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

export default PatternOverlay
