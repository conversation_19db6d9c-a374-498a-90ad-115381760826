import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Pattern {
  id: string
  name: string
  type: 'bullish' | 'bearish' | 'neutral'
  confidence: number
  time: string
  price: number
  description: string
  position: { x: number; y: number }
}

interface PatternChipsProps {
  patterns: Pattern[]
  chartWidth: number
  chartHeight: number
  onPatternClick?: (pattern: Pattern) => void
}

const PatternChips: React.FC<PatternChipsProps> = ({
  patterns,
  chartWidth,
  chartHeight,
  onPatternClick
}) => {
  const getPatternColor = (type: string, confidence: number) => {
    const opacity = Math.max(0.7, confidence / 100)
    
    switch (type) {
      case 'bullish':
        return `rgba(34, 197, 94, ${opacity})` // Green
      case 'bearish':
        return `rgba(239, 68, 68, ${opacity})` // Red
      default:
        return `rgba(156, 163, 175, ${opacity})` // Gray
    }
  }

  const getPatternIcon = (name: string) => {
    const iconMap: { [key: string]: string } = {
      'doji': '✦',
      'hammer': '🔨',
      'shooting_star': '⭐',
      'bullish_engulfing': '📈',
      'bearish_engulfing': '📉',
      'morning_star': '🌅',
      'evening_star': '🌆',
      'hanging_man': '👤',
      'inverted_hammer': '🔨',
      'dark_cloud': '☁️',
      'piercing_line': '⚡',
      'three_white_soldiers': '⬆️',
      'three_black_crows': '⬇️',
    }
    
    return iconMap[name.toLowerCase().replace(' ', '_')] || '📊'
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 80) return 'HIGH'
    if (confidence >= 60) return 'MED'
    return 'LOW'
  }

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      <AnimatePresence>
        {patterns.map((pattern) => (
          <motion.div
            key={pattern.id}
            initial={{ opacity: 0, scale: 0.5, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="absolute pointer-events-auto cursor-pointer"
            style={{
              left: `${pattern.position.x}px`,
              top: `${pattern.position.y}px`,
              transform: 'translate(-50%, -100%)',
            }}
            onClick={() => onPatternClick?.(pattern)}
          >
            {/* Pattern Chip */}
            <div
              className="relative flex items-center space-x-2 px-3 py-1.5 rounded-full text-white text-xs font-medium shadow-lg border border-white/20 backdrop-blur-sm"
              style={{
                backgroundColor: getPatternColor(pattern.type, pattern.confidence),
              }}
            >
              {/* Pattern Icon */}
              <span className="text-sm">{getPatternIcon(pattern.name)}</span>
              
              {/* Pattern Name */}
              <span className="font-semibold">{pattern.name}</span>
              
              {/* Confidence Badge */}
              <span
                className={`px-1.5 py-0.5 rounded text-xs font-bold ${
                  pattern.confidence >= 80
                    ? 'bg-green-500/80 text-white'
                    : pattern.confidence >= 60
                    ? 'bg-yellow-500/80 text-white'
                    : 'bg-red-500/80 text-white'
                }`}
              >
                {getConfidenceLabel(pattern.confidence)}
              </span>
              
              {/* Confidence Percentage */}
              <span className="text-xs opacity-90">
                {pattern.confidence}%
              </span>
            </div>

            {/* Connecting Line */}
            <div
              className="absolute top-full left-1/2 w-0.5 h-4 -translate-x-1/2"
              style={{
                backgroundColor: getPatternColor(pattern.type, pattern.confidence),
              }}
            />

            {/* Price Point */}
            <div
              className="absolute top-full left-1/2 w-2 h-2 rounded-full -translate-x-1/2 translate-y-3"
              style={{
                backgroundColor: getPatternColor(pattern.type, pattern.confidence),
                boxShadow: `0 0 8px ${getPatternColor(pattern.type, pattern.confidence)}`,
              }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

export default PatternChips
