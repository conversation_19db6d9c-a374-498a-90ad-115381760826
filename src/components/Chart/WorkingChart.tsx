import React, { useEffect, useRef, useState } from 'react'
import { create<PERSON><PERSON>, IChartApi, ISeriesApi } from 'lightweight-charts'
import { detectCandlestickPatterns } from '@/utils/enhancedPatternDetection'

interface WorkingChartProps {
  symbol: string
  height?: number
}

interface PatternChip {
  id: string
  name: string
  type: 'bullish' | 'bearish' | 'neutral'
  confidence: number
  x: number
  y: number
  price: number
}

const WorkingChart: React.FC<WorkingChartProps> = ({ symbol, height = 600 }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [patterns, setPatterns] = useState<PatternChip[]>([])
  const [stockInfo, setStockInfo] = useState<any>(null)

  useEffect(() => {
    if (!chartContainerRef.current) return

    console.log('🚀 Creating working chart for', symbol)

    // Create chart with professional styling
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: height - 100,
      layout: {
        background: { color: '#0f172a' },
        textColor: '#e2e8f0',
      },
      grid: {
        vertLines: { color: '#1e293b' },
        horzLines: { color: '#1e293b' },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          color: '#64748b',
          width: 1,
          style: 2,
        },
        horzLine: {
          color: '#64748b',
          width: 1,
          style: 2,
        },
      },
      rightPriceScale: {
        borderColor: '#334155',
        textColor: '#e2e8f0',
      },
      timeScale: {
        borderColor: '#334155',
        textColor: '#e2e8f0',
        timeVisible: true,
        secondsVisible: false,
      },
      watermark: {
        visible: true,
        fontSize: 24,
        horzAlign: 'center',
        vertAlign: 'center',
        color: 'rgba(59, 130, 246, 0.1)',
        text: `${symbol} - NEPSE`,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderUpColor: '#10b981',
      borderDownColor: '#ef4444',
      wickUpColor: '#10b981',
      wickDownColor: '#ef4444',
    })

    candlestickSeriesRef.current = candlestickSeries

    // Load data
    loadChartData()

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
    }
  }, [symbol, height])

  const loadChartData = async () => {
    try {
      setIsLoading(true)
      console.log(`📊 Loading data for ${symbol}`)

      const response = await fetch(`/api/stocks/${symbol}/candlestick?timeframe=1D&limit=200`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      console.log('📈 API Response:', result)

      if (!result.success || !result.data || result.data.length === 0) {
        console.log('⚠️ No data, using sample data')
        loadSampleData()
        return
      }

      // Format data for TradingView
      const formattedData = result.data.map((item: any) => {
        const date = new Date(item.time || item.date)
        return {
          time: date.toISOString().split('T')[0],
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
        }
      }).reverse() // Oldest first

      console.log('✅ Formatted data:', formattedData.length, 'candles')
      console.log('📊 Sample:', formattedData.slice(0, 3))

      // Set data to chart
      if (candlestickSeriesRef.current) {
        candlestickSeriesRef.current.setData(formattedData)
      }

      // Detect patterns
      const detectedPatterns = detectCandlestickPatterns(formattedData)
      console.log('🔍 Detected patterns:', detectedPatterns.length)

      // Convert patterns to visual chips
      const patternChips: PatternChip[] = detectedPatterns.slice(0, 10).map((pattern, index) => ({
        id: pattern.id,
        name: pattern.name,
        type: pattern.type,
        confidence: pattern.confidence,
        x: 100 + (index * 80), // Spread horizontally
        y: 50 + (index % 3) * 40, // Stack vertically
        price: pattern.price,
      }))

      setPatterns(patternChips)

      // Set stock info
      const latest = result.data[0]
      setStockInfo({
        symbol,
        price: parseFloat(latest.close),
        change: parseFloat(latest.close) - parseFloat(latest.open),
        volume: parseFloat(latest.volume || 0),
        high: parseFloat(latest.high),
        low: parseFloat(latest.low),
      })

      setIsLoading(false)

    } catch (error) {
      console.error('❌ Error loading data:', error)
      loadSampleData()
    }
  }

  const loadSampleData = () => {
    const sampleData = [
      { time: '2024-01-01', open: 1000, high: 1050, low: 980, close: 1020 },
      { time: '2024-01-02', open: 1020, high: 1080, low: 1000, close: 1060 },
      { time: '2024-01-03', open: 1060, high: 1100, low: 1040, close: 1080 },
      { time: '2024-01-04', open: 1080, high: 1120, low: 1050, close: 1070 },
      { time: '2024-01-05', open: 1070, high: 1090, low: 1030, close: 1050 },
    ]

    if (candlestickSeriesRef.current) {
      candlestickSeriesRef.current.setData(sampleData)
    }

    setStockInfo({
      symbol,
      price: 1050,
      change: -20,
      volume: 150000,
      high: 1090,
      low: 1030,
    })

    setIsLoading(false)
  }

  const getPatternColor = (type: string) => {
    switch (type) {
      case 'bullish': return 'bg-green-500'
      case 'bearish': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPatternIcon = (name: string) => {
    const icons: { [key: string]: string } = {
      'doji': '✦',
      'hammer': '🔨',
      'shooting star': '⭐',
      'bullish engulfing': '📈',
      'bearish engulfing': '📉',
      'morning star': '🌅',
      'evening star': '🌆',
    }
    return icons[name.toLowerCase()] || '📊'
  }

  return (
    <div className="w-full bg-slate-900 rounded-lg overflow-hidden" style={{ height: `${height}px` }}>
      {/* Header */}
      <div className="bg-slate-800 p-4 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">{symbol}</h2>
            <p className="text-slate-400 text-sm">NEPSE Stock Chart</p>
          </div>
          {stockInfo && (
            <div className="text-right">
              <div className="text-2xl font-bold text-white">
                NPR {stockInfo.price.toFixed(2)}
              </div>
              <div className={`text-sm ${stockInfo.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {stockInfo.change >= 0 ? '+' : ''}{stockInfo.change.toFixed(2)} 
                ({((stockInfo.change / stockInfo.price) * 100).toFixed(2)}%)
              </div>
              <div className="text-xs text-slate-400">
                H: {stockInfo.high.toFixed(2)} L: {stockInfo.low.toFixed(2)} Vol: {stockInfo.volume.toLocaleString()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-900/80 z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-slate-400">Loading chart...</p>
            </div>
          </div>
        )}

        <div
          ref={chartContainerRef}
          className="w-full"
          style={{ height: `${height - 100}px` }}
        />

        {/* Pattern Chips Overlay */}
        {patterns.length > 0 && (
          <div className="absolute top-4 left-4 right-4 pointer-events-none">
            <div className="flex flex-wrap gap-2">
              {patterns.map((pattern) => (
                <div
                  key={pattern.id}
                  className={`${getPatternColor(pattern.type)} text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-lg pointer-events-auto cursor-pointer hover:scale-105 transition-transform`}
                  title={`${pattern.name} - ${pattern.confidence}% confidence`}
                >
                  <span>{getPatternIcon(pattern.name)}</span>
                  <span>{pattern.name}</span>
                  <span className="bg-white/20 px-1 rounded text-xs">
                    {pattern.confidence}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WorkingChart
