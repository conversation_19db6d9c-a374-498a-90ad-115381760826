import React, { useState } from 'react'
import { motion } from 'framer-motion'
import <PERSON><PERSON><PERSON> from './ProfessionalChart'
import <PERSON><PERSON>ool<PERSON> from './ChartToolbar'
import ChartSidebar from './ChartSidebar'

const ProfessionalChartLayout: React.FC = () => {
  const [currentSymbol, setCurrentSymbol] = useState('HRL')
  const [timeframe, setTimeframe] = useState('1D')
  const [sidebarVisible, setSidebarVisible] = useState(true)

  const handleSymbolChange = (symbol: string) => {
    setCurrentSymbol(symbol)
  }

  const handleTimeframeChange = (tf: string) => {
    setTimeframe(tf)
  }

  return (
    <div className="h-screen bg-gray-900 flex flex-col overflow-hidden">
      {/* Toolbar */}
      <ChartToolbar
        symbol={currentSymbol}
        onSymbolChange={handleSymbolChange}
        timeframe={timeframe}
        onTimeframeChange={handleTimeframeChange}
      />

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chart Area */}
        <div className="flex-1 relative">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            <ProfessionalChart
              symbol={currentSymbol}
              height={window.innerHeight - 60} // Subtract toolbar height
            />
          </motion.div>

          {/* Toggle Sidebar Button */}
          <button
            onClick={() => setSidebarVisible(!sidebarVisible)}
            className="absolute top-4 right-4 z-20 bg-gray-800/90 backdrop-blur-sm hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
            title={sidebarVisible ? 'Hide Sidebar' : 'Show Sidebar'}
          >
            <svg
              className={`h-5 w-5 transition-transform ${sidebarVisible ? 'rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>

        {/* Sidebar */}
        <motion.div
          initial={false}
          animate={{
            width: sidebarVisible ? 320 : 0,
            opacity: sidebarVisible ? 1 : 0,
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="overflow-hidden"
        >
          {sidebarVisible && <ChartSidebar />}
        </motion.div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 px-4 py-2 flex items-center justify-between text-sm text-gray-400">
        <div className="flex items-center space-x-4">
          <span>Market: Open</span>
          <span>•</span>
          <span>Last Update: {new Date().toLocaleTimeString()}</span>
          <span>•</span>
          <span>Data: Real-time</span>
        </div>
        <div className="flex items-center space-x-4">
          <span>NEPSE: 2,150.45 (+1.2%)</span>
          <span>•</span>
          <span>Volume: 1.2M</span>
        </div>
      </div>
    </div>
  )
}

export default ProfessionalChartLayout
