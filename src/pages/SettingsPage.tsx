import React from 'react'
import { motion } from 'framer-motion'
import { Settings, Palette, Bell, Shield, Database, Download } from 'lucide-react'

const SettingsPage: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Settings</h1>
        <p className="text-slate-400 mt-1">
          Configure your trading platform preferences
        </p>
      </div>

      {/* General Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">General</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Default Timeframe
            </label>
            <select className="input-primary">
              <option>1D</option>
              <option>1W</option>
              <option>1M</option>
              <option>3M</option>
              <option>6M</option>
              <option>1Y</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Chart Layout
            </label>
            <select className="input-primary">
              <option>Single Chart</option>
              <option>Multi-Chart</option>
              <option>Dashboard View</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Auto-refresh Interval
            </label>
            <select className="input-primary">
              <option>5 seconds</option>
              <option>10 seconds</option>
              <option>30 seconds</option>
              <option>1 minute</option>
              <option>Disabled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Language
            </label>
            <select className="input-primary">
              <option>English</option>
              <option>नेपाली (Nepali)</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Appearance Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Palette className="h-6 w-6 text-purple-400" />
            <h2 className="text-xl font-semibold text-white">Appearance</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Theme
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="radio" name="theme" defaultChecked className="mr-2" />
                <span className="text-slate-300">Dark Theme</span>
              </label>
              <label className="flex items-center">
                <input type="radio" name="theme" className="mr-2" />
                <span className="text-slate-300">Light Theme</span>
              </label>
              <label className="flex items-center">
                <input type="radio" name="theme" className="mr-2" />
                <span className="text-slate-300">Auto (System)</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Chart Colors
            </label>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-slate-300">Bullish Candles</span>
                <input type="color" defaultValue="#10b981" className="w-8 h-8 rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-300">Bearish Candles</span>
                <input type="color" defaultValue="#ef4444" className="w-8 h-8 rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-300">Grid Lines</span>
                <input type="color" defaultValue="#334155" className="w-8 h-8 rounded" />
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Analysis Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Database className="h-6 w-6 text-green-400" />
            <h2 className="text-xl font-semibold text-white">Analysis</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-medium text-white mb-3">Pattern Detection</h3>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Enable Pattern Detection</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Show Pattern Labels</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Pattern Alerts</span>
              </label>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-white mb-3">ICT Analysis</h3>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Order Blocks</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Fair Value Gaps</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Kill Zones</span>
              </label>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-white mb-3">SMC Analysis</h3>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Market Structure</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Liquidity Sweeps</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Premium/Discount Zones</span>
              </label>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Notifications */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Bell className="h-6 w-6 text-yellow-400" />
            <h2 className="text-xl font-semibold text-white">Notifications</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-white mb-3">Alert Types</h3>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Pattern Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Price Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Volume Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Strategy Signals</span>
              </label>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-white mb-3">Delivery Methods</h3>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Browser Notifications</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Email Notifications</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">SMS Notifications</span>
              </label>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Data & Privacy */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Shield className="h-6 w-6 text-red-400" />
            <h2 className="text-xl font-semibold text-white">Data & Privacy</h2>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-white">Data Collection</h3>
              <p className="text-sm text-slate-400">Allow anonymous usage analytics</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-white">Cache Data</h3>
              <p className="text-sm text-slate-400">Store chart data locally for faster loading</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" defaultChecked className="sr-only peer" />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="pt-4 border-t border-slate-700">
            <button className="btn-secondary inline-flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export Settings</span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Save Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="flex justify-end"
      >
        <button className="btn-primary">
          Save Settings
        </button>
      </motion.div>
    </div>
  )
}

export default SettingsPage
