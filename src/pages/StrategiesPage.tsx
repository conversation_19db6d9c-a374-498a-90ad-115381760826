import React from 'react'
import { motion } from 'framer-motion'
import { Target, Brain, TrendingUp, Zap, Eye, DollarSign } from 'lucide-react'

const StrategiesPage: React.FC = () => {
  const strategies = [
    {
      category: 'ICT (Inner Circle Trader)',
      icon: Brain,
      color: 'blue',
      concepts: [
        {
          name: 'Order Blocks',
          description: 'Institutional order flow analysis',
          features: ['Bullish/Bearish identification', 'Strength scoring', 'Mitigation tracking']
        },
        {
          name: 'Fair Value Gaps',
          description: 'Market inefficiency detection',
          features: ['Gap classification', 'Fill probability', 'Immediate rebalance']
        },
        {
          name: 'Liquidity Concepts',
          description: 'Smart money liquidity analysis',
          features: ['Buy/Sell side liquidity', 'Liquidity sweeps', 'Stop hunts']
        },
        {
          name: 'Kill Zones',
          description: 'Time-based trading sessions',
          features: ['London session', 'New York session', 'Asian session']
        }
      ]
    },
    {
      category: 'SMC (Smart Money Concepts)',
      icon: Eye,
      color: 'purple',
      concepts: [
        {
          name: 'Market Structure',
          description: 'Institutional market structure analysis',
          features: ['Break of Structure (BOS)', 'Change of Character (ChoCH)', 'Market Structure Shift (MSS)']
        },
        {
          name: 'Liquidity Sweeps',
          description: 'Smart money liquidity manipulation',
          features: ['Liquidity grabs', 'Stop hunts', 'Sweep confirmation']
        },
        {
          name: 'Premium/Discount Zones',
          description: 'Value-based trading zones',
          features: ['Fibonacci levels', 'Optimal Trade Entry (OTE)', 'Risk-reward zones']
        },
        {
          name: 'Order Flow',
          description: 'Institutional order flow tracking',
          features: ['Order block mitigation', 'Imbalance analysis', 'Volume confirmation']
        }
      ]
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-500/20',
          text: 'text-blue-400',
          border: 'border-blue-500/50'
        }
      case 'purple':
        return {
          bg: 'bg-purple-500/20',
          text: 'text-purple-400',
          border: 'border-purple-500/50'
        }
      default:
        return {
          bg: 'bg-slate-500/20',
          text: 'text-slate-400',
          border: 'border-slate-500/50'
        }
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Trading Strategies</h1>
        <p className="text-slate-400 mt-1">
          Advanced ICT and SMC methodologies for professional trading
        </p>
      </div>

      {/* Strategy Categories */}
      <div className="space-y-8">
        {strategies.map((strategy, strategyIndex) => {
          const Icon = strategy.icon
          const colors = getColorClasses(strategy.color)
          
          return (
            <motion.div
              key={strategy.category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: strategyIndex * 0.2 }}
              className="card"
            >
              <div className="card-header">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${colors.bg}`}>
                    <Icon className={`h-6 w-6 ${colors.text}`} />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-white">{strategy.category}</h2>
                    <p className="text-slate-400 text-sm">Professional trading methodology</p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {strategy.concepts.map((concept, conceptIndex) => (
                  <motion.div
                    key={concept.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: (strategyIndex * 0.2) + (conceptIndex * 0.1) }}
                    className={`bg-slate-700 rounded-lg p-4 border ${colors.border} hover:bg-slate-600 transition-colors`}
                  >
                    <h3 className="font-semibold text-white mb-2">{concept.name}</h3>
                    <p className="text-slate-400 text-sm mb-4">{concept.description}</p>
                    
                    <div className="space-y-2">
                      <h4 className="text-xs font-medium text-slate-300 uppercase tracking-wide">Features</h4>
                      <ul className="space-y-1">
                        {concept.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-slate-300">
                            <div className={`h-1.5 w-1.5 rounded-full ${colors.bg} mr-2`}></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Strategy Builder */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <Target className="h-6 w-6 text-green-400" />
            <h2 className="text-xl font-semibold text-white">Strategy Builder</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-slate-700 rounded-lg p-4">
            <Zap className="h-8 w-8 text-yellow-400 mb-3" />
            <h3 className="font-medium text-white mb-2">Visual Builder</h3>
            <p className="text-slate-400 text-sm">
              Drag-and-drop interface for creating custom trading strategies
            </p>
          </div>
          <div className="bg-slate-700 rounded-lg p-4">
            <TrendingUp className="h-8 w-8 text-green-400 mb-3" />
            <h3 className="font-medium text-white mb-2">Backtesting</h3>
            <p className="text-slate-400 text-sm">
              Test strategies against historical data with realistic execution
            </p>
          </div>
          <div className="bg-slate-700 rounded-lg p-4">
            <DollarSign className="h-8 w-8 text-blue-400 mb-3" />
            <h3 className="font-medium text-white mb-2">Paper Trading</h3>
            <p className="text-slate-400 text-sm">
              Live strategy execution with virtual portfolio management
            </p>
          </div>
        </div>
      </motion.div>

      {/* Configuration Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Strategy Configuration</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              ICT Analysis
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Order Blocks</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Fair Value Gaps</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Kill Zones</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              SMC Analysis
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Market Structure</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Liquidity Sweeps</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Premium/Discount</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Risk Management
            </label>
            <div className="space-y-2">
              <div>
                <label className="text-xs text-slate-400">Risk per Trade</label>
                <input type="number" defaultValue="2" className="input-primary text-sm" />
              </div>
              <div>
                <label className="text-xs text-slate-400">Max Drawdown</label>
                <input type="number" defaultValue="10" className="input-primary text-sm" />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Alerts
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Signal Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Risk Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Performance Alerts</span>
              </label>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default StrategiesPage
