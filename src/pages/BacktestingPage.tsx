import React from 'react'
import { motion } from 'framer-motion'
import { TestTube, Play, BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react'

const BacktestingPage: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Strategy Backtesting</h1>
        <p className="text-slate-400 mt-1">
          Test your trading strategies against historical data
        </p>
      </div>

      {/* Backtesting Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card"
      >
        <div className="card-header">
          <div className="flex items-center space-x-3">
            <TestTube className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Backtest Configuration</h2>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Strategy
            </label>
            <select className="input-primary">
              <option>ICT Order Blocks</option>
              <option>SMC Market Structure</option>
              <option>Pattern Recognition</option>
              <option>Custom Strategy</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Symbol
            </label>
            <select className="input-primary">
              <option>NABIL</option>
              <option>ADBL</option>
              <option>EBL</option>
              <option>All Symbols</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Date Range
            </label>
            <div className="space-y-2">
              <input type="date" className="input-primary text-sm" />
              <input type="date" className="input-primary text-sm" />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Initial Capital
            </label>
            <input type="number" defaultValue="100000" className="input-primary" />
            <button className="btn-primary w-full mt-3">
              <Play className="h-4 w-4 mr-2" />
              Run Backtest
            </button>
          </div>
        </div>
      </motion.div>

      {/* Results Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Return</p>
              <p className="text-2xl font-bold text-green-400">+24.5%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-400" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Win Rate</p>
              <p className="text-2xl font-bold text-blue-400">68.3%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-400" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Max Drawdown</p>
              <p className="text-2xl font-bold text-red-400">-8.2%</p>
            </div>
            <TrendingDown className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Profit Factor</p>
              <p className="text-2xl font-bold text-purple-400">1.85</p>
            </div>
            <DollarSign className="h-8 w-8 text-purple-400" />
          </div>
        </div>
      </motion.div>

      {/* Equity Curve Placeholder */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Equity Curve</h2>
        </div>
        
        <div className="h-64 bg-slate-700 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-slate-600 mx-auto mb-2" />
            <p className="text-slate-400">Equity curve chart will be displayed here</p>
          </div>
        </div>
      </motion.div>

      {/* Detailed Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Detailed Metrics</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <h3 className="font-medium text-white mb-3">Performance</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-400">Total Trades</span>
                <span className="text-white">156</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Winning Trades</span>
                <span className="text-green-400">107</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Losing Trades</span>
                <span className="text-red-400">49</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Average Trade</span>
                <span className="text-white">+2.1%</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-white mb-3">Risk Metrics</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-400">Sharpe Ratio</span>
                <span className="text-white">1.42</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Sortino Ratio</span>
                <span className="text-white">2.18</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Calmar Ratio</span>
                <span className="text-white">2.99</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Beta</span>
                <span className="text-white">0.85</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-white mb-3">Trade Analysis</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-400">Avg Win</span>
                <span className="text-green-400">+4.2%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Avg Loss</span>
                <span className="text-red-400">-1.8%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Largest Win</span>
                <span className="text-green-400">+12.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Largest Loss</span>
                <span className="text-red-400">-4.1%</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Trade List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Recent Trades</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-slate-700">
                <th className="text-left py-2 text-slate-400">Date</th>
                <th className="text-left py-2 text-slate-400">Symbol</th>
                <th className="text-left py-2 text-slate-400">Type</th>
                <th className="text-left py-2 text-slate-400">Entry</th>
                <th className="text-left py-2 text-slate-400">Exit</th>
                <th className="text-left py-2 text-slate-400">P&L</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-slate-800">
                <td className="py-2 text-slate-300">2024-01-15</td>
                <td className="py-2 text-white">NABIL</td>
                <td className="py-2 text-green-400">Long</td>
                <td className="py-2 text-slate-300">1,250</td>
                <td className="py-2 text-slate-300">1,310</td>
                <td className="py-2 text-green-400">+4.8%</td>
              </tr>
              <tr className="border-b border-slate-800">
                <td className="py-2 text-slate-300">2024-01-12</td>
                <td className="py-2 text-white">ADBL</td>
                <td className="py-2 text-red-400">Short</td>
                <td className="py-2 text-slate-300">295</td>
                <td className="py-2 text-slate-300">290</td>
                <td className="py-2 text-green-400">+1.7%</td>
              </tr>
              <tr className="border-b border-slate-800">
                <td className="py-2 text-slate-300">2024-01-10</td>
                <td className="py-2 text-white">EBL</td>
                <td className="py-2 text-green-400">Long</td>
                <td className="py-2 text-slate-300">680</td>
                <td className="py-2 text-slate-300">665</td>
                <td className="py-2 text-red-400">-2.2%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  )
}

export default BacktestingPage
