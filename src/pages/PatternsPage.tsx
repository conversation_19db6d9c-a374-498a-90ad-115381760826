import React from 'react'
import { motion } from 'framer-motion'
import { Activity, TrendingUp, Minus } from 'lucide-react'

const PatternsPage: React.FC = () => {
  const patternCategories = [
    {
      title: 'Single Candlestick Patterns',
      icon: Minus,
      patterns: [
        { name: '<PERSON><PERSON>', type: 'neutral', description: 'Indecision pattern' },
        { name: '<PERSON>', type: 'bullish', description: 'Bullish reversal' },
        { name: 'Shooting Star', type: 'bearish', description: 'Bearish reversal' },
        { name: 'Maruboz<PERSON>', type: 'continuation', description: 'Strong directional move' },
      ]
    },
    {
      title: 'Two Candlestick Patterns',
      icon: TrendingUp,
      patterns: [
        { name: 'Bullish Engulfing', type: 'bullish', description: 'Strong bullish reversal' },
        { name: 'Bearish Engulfing', type: 'bearish', description: 'Strong bearish reversal' },
        { name: 'Harami', type: 'reversal', description: 'Potential reversal signal' },
        { name: 'Piercing Pattern', type: 'bullish', description: 'Bullish reversal pattern' },
      ]
    },
    {
      title: 'Three Candlestick Patterns',
      icon: Activity,
      patterns: [
        { name: 'Morning Star', type: 'bullish', description: 'Strong bullish reversal' },
        { name: 'Evening Star', type: 'bearish', description: 'Strong bearish reversal' },
        { name: 'Three White Soldiers', type: 'bullish', description: 'Strong bullish continuation' },
        { name: 'Three Black Crows', type: 'bearish', description: 'Strong bearish continuation' },
      ]
    }
  ]

  const getPatternColor = (type: string) => {
    switch (type) {
      case 'bullish':
        return 'text-green-400 bg-green-500/20'
      case 'bearish':
        return 'text-red-400 bg-red-500/20'
      case 'neutral':
        return 'text-slate-400 bg-slate-500/20'
      case 'reversal':
        return 'text-blue-400 bg-blue-500/20'
      case 'continuation':
        return 'text-purple-400 bg-purple-500/20'
      default:
        return 'text-slate-400 bg-slate-500/20'
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Candlestick Patterns</h1>
        <p className="text-slate-400 mt-1">
          Comprehensive candlestick pattern recognition and analysis
        </p>
      </div>

      {/* Pattern Categories */}
      <div className="space-y-8">
        {patternCategories.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: categoryIndex * 0.1 }}
              className="card"
            >
              <div className="card-header">
                <div className="flex items-center space-x-3">
                  <Icon className="h-6 w-6 text-blue-400" />
                  <h2 className="text-xl font-semibold text-white">{category.title}</h2>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {category.patterns.map((pattern, patternIndex) => (
                  <motion.div
                    key={pattern.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: (categoryIndex * 0.1) + (patternIndex * 0.05) }}
                    className="bg-slate-700 rounded-lg p-4 hover:bg-slate-600 transition-colors cursor-pointer"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-white">{pattern.name}</h3>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getPatternColor(pattern.type)}`}>
                        {pattern.type}
                      </span>
                    </div>
                    <p className="text-slate-400 text-sm">{pattern.description}</p>

                    {/* Placeholder for pattern visualization */}
                    <div className="mt-3 h-16 bg-slate-800 rounded flex items-center justify-center">
                      <Activity className="h-6 w-6 text-slate-600" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Pattern Detection Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Detection Settings</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Minimum Confidence
            </label>
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="70"
              className="w-full"
            />
            <div className="flex justify-between text-xs text-slate-400 mt-1">
              <span>0%</span>
              <span>70%</span>
              <span>100%</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Pattern Types
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Reversal Patterns</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Continuation Patterns</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Neutral Patterns</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Alerts
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Real-time Alerts</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2" />
                <span className="text-sm text-slate-300">Email Notifications</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-slate-300">Show on Chart</span>
              </label>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Coming Soon Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="card"
      >
        <div className="card-header">
          <h2 className="text-xl font-semibold text-white">Coming Soon</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-slate-700 rounded-lg p-4">
            <h3 className="font-medium text-white mb-2">Pattern Performance Analytics</h3>
            <p className="text-slate-400 text-sm">
              Historical success rates and performance metrics for each pattern
            </p>
          </div>
          <div className="bg-slate-700 rounded-lg p-4">
            <h3 className="font-medium text-white mb-2">Custom Pattern Builder</h3>
            <p className="text-slate-400 text-sm">
              Create and test your own custom candlestick patterns
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default PatternsPage
