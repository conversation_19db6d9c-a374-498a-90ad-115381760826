import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import {
  TrendingUp,
  Activity,
  BarChart3,
  ArrowRight,
  RefreshCw
} from 'lucide-react'
import { motion } from 'framer-motion'
import { apiService } from '@/services/api'


interface MarketStats {
  totalCompanies: number
  activeCompanies: number
  topGainers: any[]
  topLosers: any[]
  topVolume: any[]
}

const Dashboard: React.FC = () => {
  const [marketStats, setMarketStats] = useState<MarketStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch market data
      const [companiesResponse, gainersResponse, losersResponse, volumeResponse] = await Promise.all([
        apiService.getCompanies({ is_active: true, limit: 1 }),
        apiService.getTopGainers(5),
        apiService.getTopLosers(5),
        apiService.getTopVolume(5),
      ])

      setMarketStats({
        totalCompanies: companiesResponse.total || 0,
        activeCompanies: companiesResponse.total || 0,
        topGainers: gainersResponse.data || [],
        topLosers: losersResponse.data || [],
        topVolume: volumeResponse.data || [],
      })
    } catch (err) {
      console.error('Failed to load dashboard data:', err)
      setError('Failed to load market data. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-slate-800 rounded-lg p-6 h-32"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="bg-slate-800 rounded-lg p-6 h-64"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button
            onClick={loadDashboardData}
            className="btn-primary inline-flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Companies',
      value: marketStats?.totalCompanies || 0,
      icon: BarChart3,
      color: 'blue',
      change: null,
    },
    {
      title: 'Active Companies',
      value: marketStats?.activeCompanies || 0,
      icon: Activity,
      color: 'green',
      change: null,
    },
    {
      title: 'Top Gainers',
      value: marketStats?.topGainers.length || 0,
      icon: TrendingUp,
      color: 'green',
      change: '+12.5%',
    },
    {
      title: 'Top Losers',
      value: marketStats?.topLosers.length || 0,
      icon: Activity,
      color: 'red',
      change: '-8.3%',
    },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Market Dashboard</h1>
          <p className="text-slate-400 mt-1">
            Real-time overview of NEPSE market performance
          </p>
        </div>
        <button
          onClick={loadDashboardData}
          className="btn-secondary inline-flex items-center space-x-2"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                  {stat.change && (
                    <p className={`text-sm mt-1 ${
                      stat.change.startsWith('+') ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {stat.change}
                    </p>
                  )}
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-500/20' :
                  stat.color === 'green' ? 'bg-green-500/20' :
                  'bg-red-500/20'
                }`}>
                  <Icon className={`h-6 w-6 ${
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    'text-red-400'
                  }`} />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Link
              to="/chart"
              className="flex items-center justify-between p-3 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <BarChart3 className="h-5 w-5 text-blue-400" />
                <span className="text-white">Open Chart</span>
              </div>
              <ArrowRight className="h-4 w-4 text-slate-400" />
            </Link>
            <Link
              to="/patterns"
              className="flex items-center justify-between p-3 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5 text-green-400" />
                <span className="text-white">Pattern Analysis</span>
              </div>
              <ArrowRight className="h-4 w-4 text-slate-400" />
            </Link>
            <Link
              to="/strategies"
              className="flex items-center justify-between p-3 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <TrendingUp className="h-5 w-5 text-purple-400" />
                <span className="text-white">Trading Strategies</span>
              </div>
              <ArrowRight className="h-4 w-4 text-slate-400" />
            </Link>
          </div>
        </motion.div>

        {/* Market Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-white mb-4">Market Status</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Status</span>
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-red-400">Closed</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Next Open</span>
              <span className="text-white">Tomorrow 10:00 AM</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Last Update</span>
              <span className="text-white">5:00 PM</span>
            </div>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="text-sm">
              <p className="text-slate-400">No recent activity</p>
              <p className="text-xs text-slate-500 mt-1">
                Start analyzing charts to see activity here
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Feature Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="card"
      >
        <h3 className="text-lg font-semibold text-white mb-4">Platform Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-500/20 p-4 rounded-lg mb-3 inline-block">
              <BarChart3 className="h-8 w-8 text-blue-400" />
            </div>
            <h4 className="text-white font-medium mb-2">Advanced Charting</h4>
            <p className="text-slate-400 text-sm">
              Professional-grade charts with TradingView integration
            </p>
          </div>
          <div className="text-center">
            <div className="bg-green-500/20 p-4 rounded-lg mb-3 inline-block">
              <Activity className="h-8 w-8 text-green-400" />
            </div>
            <h4 className="text-white font-medium mb-2">Pattern Recognition</h4>
            <p className="text-slate-400 text-sm">
              Automated candlestick pattern detection and analysis
            </p>
          </div>
          <div className="text-center">
            <div className="bg-purple-500/20 p-4 rounded-lg mb-3 inline-block">
              <TrendingUp className="h-8 w-8 text-purple-400" />
            </div>
            <h4 className="text-white font-medium mb-2">ICT & SMC Analysis</h4>
            <p className="text-slate-400 text-sm">
              Smart Money Concepts and Inner Circle Trader methodologies
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default Dashboard
