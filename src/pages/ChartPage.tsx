import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import ProfessionalChartLayout from '@/components/Chart/ProfessionalChartLayout'
import SimpleChart from '@/components/Chart/SimpleChart'
import WorkingChart from '@/components/Chart/WorkingChart'

const ChartPage: React.FC = () => {
  const { symbol } = useParams<{ symbol: string }>()
  const [chartType, setChartType] = useState<'working' | 'simple' | 'professional'>('working')
  const currentSymbol = symbol || 'HRL'

  return (
    <div className="h-screen bg-gray-900">
      {/* Chart Type Selector */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <h1 className="text-white font-bold text-lg">NEPSE Chart - {currentSymbol}</h1>
          <div className="flex space-x-2">
            <button
              onClick={() => setChartType('working')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                chartType === 'working'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Working Chart + Patterns
            </button>
            <button
              onClick={() => setChartType('simple')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                chartType === 'simple'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Simple Chart
            </button>
            <button
              onClick={() => setChartType('professional')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                chartType === 'professional'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Professional Layout
            </button>
          </div>
        </div>
      </div>

      {/* Chart Content */}
      <div className="flex-1 p-4">
        {chartType === 'working' && (
          <WorkingChart symbol={currentSymbol} height={700} />
        )}
        {chartType === 'simple' && (
          <SimpleChart symbol={currentSymbol} height={600} />
        )}
        {chartType === 'professional' && (
          <div className="h-full -m-4">
            <ProfessionalChartLayout />
          </div>
        )}
      </div>
    </div>
  )
}

export default ChartPage
