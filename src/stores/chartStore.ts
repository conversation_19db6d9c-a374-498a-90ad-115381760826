import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import {
  ChartState,
  TimeFrame,
  CandlestickData,
  LineData,
  PatternDetection,
  OrderBlock,
  FairValueGap,
  LiquidityLevel,
  MarketStructure
} from '@/types'

interface ChartStore extends ChartState {
  // Actions
  setSelectedSymbol: (symbol: string) => void
  setTimeframe: (timeframe: TimeFrame) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setCandlestickData: (data: CandlestickData[]) => void
  setVolumeData: (data: LineData[]) => void
  setIndicatorData: (indicator: string, data: LineData[]) => void
  addPattern: (pattern: PatternDetection) => void
  removePattern: (patternId: string) => void
  addOrderBlock: (orderBlock: OrderBlock) => void
  removeOrderBlock: (orderBlockId: string) => void
  addFairValueGap: (fvg: FairValueGap) => void
  removeFairValueGap: (fvgId: string) => void
  addLiquidityLevel: (level: LiquidityLevel) => void
  removeLiquidityLevel: (levelId: string) => void
  addMarketStructure: (structure: MarketStructure) => void
  clearAllData: () => void
  reset: () => void
}

const initialState: ChartState = {
  selectedSymbol: null,
  timeframe: '1D',
  isLoading: false,
  error: null,
  candlestickData: [],
  volumeData: [],
  indicators: {},
  patterns: [],
  orderBlocks: [],
  fairValueGaps: [],
  liquidityLevels: [],
  marketStructure: [],
}

export const useChartStore = create<ChartStore>()(
  devtools(
    (set) => ({
      ...initialState,

      // Basic setters
      setSelectedSymbol: (symbol: string) => {
        set({ selectedSymbol: symbol }, false, 'setSelectedSymbol')
      },

      setTimeframe: (timeframe: TimeFrame) => {
        set({ timeframe }, false, 'setTimeframe')
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading }, false, 'setLoading')
      },

      setError: (error: string | null) => {
        set({ error }, false, 'setError')
      },

      // Data setters
      setCandlestickData: (candlestickData: CandlestickData[]) => {
        set({ candlestickData }, false, 'setCandlestickData')
      },

      setVolumeData: (volumeData: LineData[]) => {
        set({ volumeData }, false, 'setVolumeData')
      },

      setIndicatorData: (indicator: string, data: LineData[]) => {
        set(
          (state) => ({
            indicators: {
              ...state.indicators,
              [indicator]: data,
            },
          }),
          false,
          'setIndicatorData'
        )
      },

      // Pattern management
      addPattern: (pattern: PatternDetection) => {
        set(
          (state) => ({
            patterns: [...state.patterns, pattern],
          }),
          false,
          'addPattern'
        )
      },

      removePattern: (patternId: string) => {
        set(
          (state) => ({
            patterns: state.patterns.filter((p) => p.pattern.id !== patternId),
          }),
          false,
          'removePattern'
        )
      },

      // Order Block management
      addOrderBlock: (orderBlock: OrderBlock) => {
        set(
          (state) => ({
            orderBlocks: [...state.orderBlocks, orderBlock],
          }),
          false,
          'addOrderBlock'
        )
      },

      removeOrderBlock: (orderBlockId: string) => {
        set(
          (state) => ({
            orderBlocks: state.orderBlocks.filter((ob) => ob.id !== orderBlockId),
          }),
          false,
          'removeOrderBlock'
        )
      },

      // Fair Value Gap management
      addFairValueGap: (fvg: FairValueGap) => {
        set(
          (state) => ({
            fairValueGaps: [...state.fairValueGaps, fvg],
          }),
          false,
          'addFairValueGap'
        )
      },

      removeFairValueGap: (fvgId: string) => {
        set(
          (state) => ({
            fairValueGaps: state.fairValueGaps.filter((fvg) => fvg.id !== fvgId),
          }),
          false,
          'removeFairValueGap'
        )
      },

      // Liquidity Level management
      addLiquidityLevel: (level: LiquidityLevel) => {
        set(
          (state) => ({
            liquidityLevels: [...state.liquidityLevels, level],
          }),
          false,
          'addLiquidityLevel'
        )
      },

      removeLiquidityLevel: (levelId: string) => {
        set(
          (state) => ({
            liquidityLevels: state.liquidityLevels.filter((l) => l.id !== levelId),
          }),
          false,
          'removeLiquidityLevel'
        )
      },

      // Market Structure management
      addMarketStructure: (structure: MarketStructure) => {
        set(
          (state) => ({
            marketStructure: [...state.marketStructure, structure],
          }),
          false,
          'addMarketStructure'
        )
      },

      // Utility functions
      clearAllData: () => {
        set(
          {
            candlestickData: [],
            volumeData: [],
            indicators: {},
            patterns: [],
            orderBlocks: [],
            fairValueGaps: [],
            liquidityLevels: [],
            marketStructure: [],
            error: null,
          },
          false,
          'clearAllData'
        )
      },

      reset: () => {
        set(initialState, false, 'reset')
      },
    }),
    {
      name: 'chart-store',
    }
  )
)
