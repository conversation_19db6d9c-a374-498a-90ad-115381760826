# NEPSE Advanced Charting Platform

A comprehensive, professional-grade stock charting application for the Nepal Stock Exchange (NEPSE) featuring advanced technical analysis, candlestick pattern recognition, ICT (Inner Circle Trader) concepts, and SMC (Smart Money Concepts) methodologies.

## 🚀 Features

### Core Charting
- **TradingView Lightweight Charts Integration**: Professional-grade charting with 45KB lightweight library
- **Multiple Timeframes**: 1D, 1W, 1M, 3M, 6M, 1Y, and ALL data views
- **Real-time Data**: Live market data integration with WebSocket connections
- **Interactive Charts**: Zoom, pan, crosshair, and drawing tools

### Technical Analysis
- **50+ Technical Indicators**: Moving averages, RSI, MACD, Bollinger Bands, and more
- **Custom Indicator Builder**: Create and test your own indicators
- **Volume Analysis**: Volume profile and volume-based indicators
- **Multi-timeframe Analysis**: Synchronized analysis across different timeframes

### Candlestick Pattern Recognition
- **Comprehensive Pattern Library**: 30+ candlestick patterns
- **Real-time Detection**: Automated pattern recognition as data updates
- **Pattern Analytics**: Success rates and historical performance
- **Custom Alerts**: Notifications for specific pattern formations

### ICT (Inner Circle Trader) Analysis
- **Order Blocks**: Institutional order flow analysis
- **Fair Value Gaps**: Market inefficiency detection and tracking
- **Liquidity Concepts**: Buy/sell side liquidity mapping
- **Kill Zones**: Time-based trading session analysis
- **Market Structure**: BOS, ChoCH, and MSS identification

### SMC (Smart Money Concepts) Analysis
- **Market Structure Analysis**: Advanced institutional structure tracking
- **Liquidity Sweeps**: Smart money liquidity manipulation detection
- **Premium/Discount Zones**: Value-based trading areas
- **Order Flow Analysis**: Institutional order flow tracking

### Strategy Development & Backtesting
- **Visual Strategy Builder**: Drag-and-drop strategy creation
- **Comprehensive Backtesting**: Historical strategy testing with realistic execution
- **Performance Analytics**: Detailed metrics and risk analysis
- **Paper Trading**: Live strategy execution with virtual portfolio

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **TradingView Lightweight Charts** for charting
- **Tailwind CSS** for styling
- **Zustand** for state management
- **Framer Motion** for animations
- **Vite** for build tooling

### Backend
- **Node.js** with Express and TypeScript
- **MySQL** for data storage
- **Redis** for caching
- **WebSocket** for real-time communication
- **Technical Indicators** library for calculations

## 📦 Installation

### Prerequisites
- Node.js 18+ and npm
- MySQL 8.0+
- Redis 6.0+
- Git

### 1. Clone the Repository
```bash
git clone <repository-url>
cd NepseChart
```

### 2. Install Dependencies
```bash
# Install all dependencies (frontend + backend)
npm run install:all

# Or install separately
npm install                    # Frontend dependencies
cd server && npm install      # Backend dependencies
```

### 3. Database Setup
```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE nepse_market;

# Import your existing NEPSE database schema and data
# (Assumes you have the existing database structure)
```

### 4. Environment Configuration
```bash
# Copy environment template
cp server/.env.example server/.env

# Edit the environment file with your configuration
nano server/.env
```

Required environment variables:
```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=nepse_market

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Server
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
```

### 5. Start the Application
```bash
# Development mode (runs both frontend and backend)
npm run dev

# Or start separately
npm run server        # Backend only
npm start            # Frontend only
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/health

## 🏗️ Project Structure

```
nepse-chart/
├── src/                          # Frontend source code
│   ├── components/              # React components
│   │   ├── Chart/              # Chart-related components
│   │   ├── Layout/             # Layout components
│   │   └── UI/                 # Reusable UI components
│   ├── pages/                  # Page components
│   ├── services/               # API services
│   ├── stores/                 # State management
│   ├── types/                  # TypeScript type definitions
│   ├── utils/                  # Utility functions
│   └── constants/              # Application constants
├── server/                      # Backend source code
│   ├── src/
│   │   ├── routes/             # API routes
│   │   ├── services/           # Business logic services
│   │   ├── middleware/         # Express middleware
│   │   └── types/              # Backend type definitions
│   └── dist/                   # Compiled backend code
├── public/                     # Static assets
└── docs/                       # Documentation
```

## 🔧 Development

### Available Scripts

#### Frontend
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

#### Backend
```bash
npm run server       # Start backend development server
npm run server:build # Build backend for production
```

### Code Style
- ESLint and Prettier configured
- TypeScript strict mode enabled
- Conventional commit messages recommended

## 📊 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Key Endpoints

#### Companies
- `GET /companies` - List all companies
- `GET /companies/:symbol` - Get company details

#### Stock Data
- `GET /stocks/:symbol/prices` - Get stock prices
- `GET /stocks/:symbol/candlestick` - Get candlestick data
- `GET /stocks/:symbol/latest` - Get latest price

#### Technical Analysis
- `GET /stocks/:symbol/indicators/:type` - Get technical indicators
- `GET /patterns/:symbol` - Get candlestick patterns
- `GET /ict/:symbol/order-blocks` - Get ICT order blocks
- `GET /smc/:symbol/market-structure` - Get SMC market structure

#### Market Data
- `GET /market/summary` - Market overview
- `GET /market/top-gainers` - Top gaining stocks
- `GET /market/top-losers` - Top losing stocks

## 🔄 Real-time Features

### WebSocket Connection
```javascript
const ws = new WebSocket('ws://localhost:5000')

// Subscribe to real-time updates
ws.send(JSON.stringify({
  type: 'subscribe',
  data: { symbols: ['NABIL', 'ADBL'] }
}))
```

### Supported Real-time Events
- Price updates
- Pattern detections
- Market status changes
- ICT/SMC signal alerts

## 🧪 Testing

```bash
# Run frontend tests
npm test

# Run backend tests
cd server && npm test

# Run all tests
npm run test:all
```

## 🚀 Deployment

### Production Build
```bash
# Build frontend
npm run build

# Build backend
npm run server:build

# Start production server
cd server && npm start
```

### Environment Variables for Production
```env
NODE_ENV=production
PORT=5000
DB_HOST=your_production_db_host
REDIS_HOST=your_production_redis_host
CORS_ORIGIN=https://your-domain.com
```

## 📈 Roadmap

### Phase 1: Core Infrastructure ✅
- [x] Project setup and basic charting
- [x] Database integration
- [x] API structure
- [x] Basic UI/UX

### Phase 2: Pattern Recognition (In Progress)
- [ ] Candlestick pattern detection engine
- [ ] Pattern visualization
- [ ] Pattern alerts and notifications

### Phase 3: ICT Implementation
- [ ] Order block detection
- [ ] Fair value gap analysis
- [ ] Liquidity level mapping
- [ ] Kill zone analysis

### Phase 4: SMC Implementation
- [ ] Market structure analysis
- [ ] Liquidity sweep detection
- [ ] Premium/discount zones
- [ ] Order flow tracking

### Phase 5: Strategy & Backtesting
- [ ] Strategy builder interface
- [ ] Backtesting engine
- [ ] Performance analytics
- [ ] Paper trading system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `/docs` folder
- Review the API documentation

## 🙏 Acknowledgments

- TradingView for the excellent Lightweight Charts library
- The NEPSE community for market data and insights
- ICT and SMC educators for trading methodologies
- Open source contributors and maintainers

---

**Built with ❤️ for the NEPSE trading community**
