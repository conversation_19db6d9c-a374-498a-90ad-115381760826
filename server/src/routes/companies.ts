import { Router, Request, Response } from 'express'
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler'
import DatabaseService from '../services/database'

const router = Router()
const dbService = new DatabaseService()

// GET /api/companies - Get all companies with optional filters
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const {
    search,
    sector_id,
    is_active,
    page = 1,
    limit = 50
  } = req.query

  const limitNum = limit ? Number(limit) : 50
  const pageNum = page ? Number(page) : 1
  const offset = (pageNum - 1) * limitNum

  const companies = await dbService.getCompanies({
    search: search as string,
    sector_id: sector_id ? Number(sector_id) : undefined,
    is_active: is_active === 'true' ? true : is_active === 'false' ? false : undefined,
    limit: limitNum,
    offset: offset
  })

  // Get total count for pagination
  const totalQuery = `
    SELECT COUNT(*) as total FROM Companies
    WHERE 1=1
    ${search ? 'AND (name LIKE ? OR symbol LIKE ?)' : ''}
    ${sector_id ? 'AND sector_id = ?' : ''}
    ${is_active !== undefined ? 'AND is_active = ?' : ''}
  `

  const countParams: any[] = []
  if (search) {
    countParams.push(`%${search}%`, `%${search}%`)
  }
  if (sector_id) {
    countParams.push(Number(sector_id))
  }
  if (is_active !== undefined) {
    countParams.push(is_active === 'true')
  }

  const [{ total }] = await dbService.query(totalQuery, countParams)

  res.json({
    success: true,
    data: companies,
    total: Number(total),
    page: pageNum,
    limit: limitNum,
    hasNext: offset + companies.length < total,
    hasPrev: pageNum > 1
  })
}))

// GET /api/companies/:symbol - Get company by symbol
router.get('/:symbol', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params

  const company = await dbService.getCompanyBySymbol(symbol.toUpperCase())

  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found'
    })
  }

  res.json({
    success: true,
    data: company
  })
}))

export default router
