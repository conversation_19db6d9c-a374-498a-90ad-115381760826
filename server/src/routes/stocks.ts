import { Router, Request, Response } from 'express'
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler'
import DatabaseService from '../services/database'

const router = Router()
const dbService = new DatabaseService()

// GET /api/stocks/:symbol/prices - Get stock prices
router.get('/:symbol/prices', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params
  const { timeframe, start_date, end_date, limit } = req.query

  const prices = await dbService.getStockPrices(symbol.toUpperCase(), {
    start_date: start_date as string,
    end_date: end_date as string,
    limit: limit ? Number(limit) : undefined
  })

  res.json({
    success: true,
    data: prices
  })
}))

// GET /api/stocks/:symbol/candlestick - Get candlestick data
router.get('/:symbol/candlestick', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params
  const { timeframe, start_date, end_date, limit } = req.query

  const prices = await dbService.getStockPrices(symbol.toUpperCase(), {
    start_date: start_date as string,
    end_date: end_date as string,
    limit: limit ? Number(limit) : undefined
  })

  // Transform to candlestick format
  const candlestickData = prices.map(price => ({
    time: price.date,
    open: Number(price.open_price),
    high: Number(price.high_price),
    low: Number(price.low_price),
    close: Number(price.close_price),
    volume: Number(price.volume)
  }))

  res.json({
    success: true,
    data: candlestickData
  })
}))

// GET /api/stocks/:symbol/latest - Get latest stock price
router.get('/:symbol/latest', asyncHandler(async (req: Request, res: Response) => {
  const { symbol } = req.params

  const latestPrice = await dbService.getLatestStockPrice(symbol.toUpperCase())

  if (!latestPrice) {
    return res.status(404).json({
      success: false,
      message: 'No price data found for this symbol'
    })
  }

  res.json({
    success: true,
    data: latestPrice
  })
}))

// GET /api/stocks/:symbol/indicators/:type - Get technical indicators
router.get('/:symbol/indicators/:type', asyncHandler(async (req: Request, res: Response) => {
  const { symbol, type } = req.params
  const { start_date, end_date, limit } = req.query

  const indicators = await dbService.getTechnicalIndicators(symbol.toUpperCase(), type, {
    start_date: start_date as string,
    end_date: end_date as string,
    limit: limit ? Number(limit) : undefined
  })

  res.json({
    success: true,
    data: indicators
  })
}))

// POST /api/stocks/:symbol/indicators/:type - Calculate technical indicators
router.post('/:symbol/indicators/:type', asyncHandler(async (req: Request, res: Response) => {
  const { symbol, type } = req.params
  const { parameters } = req.body

  // This is a placeholder - actual indicator calculation will be implemented later
  res.json({
    success: true,
    message: 'Indicator calculation endpoint - to be implemented',
    symbol,
    type,
    parameters
  })
}))

export default router
