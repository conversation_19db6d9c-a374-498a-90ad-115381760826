import { Router, Request, Response } from 'express'
import { async<PERSON><PERSON><PERSON> } from '../middleware/errorHandler'
import DatabaseService from '../services/database'

const router = Router()
const dbService = new DatabaseService()

// GET /api/search/stocks - Search stocks
router.get('/stocks', asyncHandler(async (req: Request, res: Response) => {
  const { q } = req.query

  if (!q || typeof q !== 'string' || q.trim().length < 2) {
    return res.status(400).json({
      success: false,
      message: 'Search query must be at least 2 characters long'
    })
  }

  const companies = await dbService.getCompanies({
    search: q.trim(),
    is_active: true,
    limit: 20
  })

  res.json({
    success: true,
    data: companies
  })
}))

export default router
