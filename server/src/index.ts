import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import { createServer } from 'http'
import { WebSocketServer } from 'ws'
import dotenv from 'dotenv'
import { rateLimiterMiddleware } from './middleware/rateLimiter'
import { errorHandler } from './middleware/errorHandler'
import { requestLogger } from './middleware/logger'
import { DatabaseService } from './services/database'
import { WebSocketService } from './services/websocket'

// Routes
import companiesRouter from './routes/companies'
import stocksRouter from './routes/stocks'
import marketRouter from './routes/market'
import searchRouter from './routes/search'
import patternsRouter from './routes/patterns'
import ictRouter from './routes/ict'
import smcRouter from './routes/smc'

// Load environment variables
dotenv.config()

const app = express()
const server = createServer(app)
const wss = new WebSocketServer({ server })

// Initialize services
const dbService = new DatabaseService()
const wsService = new WebSocketService(wss)

// Middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}))

app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
}))

app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'))
  app.use(requestLogger)
}

// Rate limiting
app.use(rateLimiterMiddleware)

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  })
})

// API Routes
app.use('/api/companies', companiesRouter)
app.use('/api/stocks', stocksRouter)
app.use('/api/market', marketRouter)
app.use('/api/search', searchRouter)
app.use('/api/patterns', patternsRouter)
app.use('/api/ict', ictRouter)
app.use('/api/smc', smcRouter)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl,
  })
})

// Error handling middleware
app.use(errorHandler)

// Initialize services and start server
async function startServer() {
  try {
    // Try to initialize database connection (optional for development)
    try {
      await dbService.connect()
      console.log('✅ Database connected successfully')
    } catch (dbError) {
      console.warn('⚠️ Database connection failed (continuing without database):', dbError.message)
    }

    // Redis disabled for now
    console.log('ℹ️ Redis disabled for initial setup')

    // Initialize WebSocket service
    wsService.initialize()
    console.log('✅ WebSocket service initialized')

    // Start server
    const PORT = process.env.PORT || 5000
    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`)
      console.log(`📊 NEPSE Advanced Charting API`)
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`)
      console.log(`🔗 Health check: http://localhost:${PORT}/health`)
    })

  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...')

  server.close(async () => {
    try {
      await dbService.disconnect()
      console.log('✅ Server shut down successfully')
      process.exit(0)
    } catch (error) {
      console.error('❌ Error during shutdown:', error)
      process.exit(1)
    }
  })
})

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...')

  server.close(async () => {
    try {
      await dbService.disconnect()
      console.log('✅ Server shut down successfully')
      process.exit(0)
    } catch (error) {
      console.error('❌ Error during shutdown:', error)
      process.exit(1)
    }
  })
})

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Start the server
startServer()

export default app
