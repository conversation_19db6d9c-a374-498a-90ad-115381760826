import { WebSocketServer, WebSocket } from 'ws'

export class WebSocketService {
  private wss: WebSocketServer
  private clients: Set<WebSocket> = new Set()
  private heartbeatInterval: NodeJS.Timeout | null = null

  constructor(wss: WebSocketServer) {
    this.wss = wss
  }

  initialize(): void {
    this.wss.on('connection', (ws: WebSocket, req) => {
      console.log(`📡 New WebSocket connection from ${req.socket.remoteAddress}`)
      
      this.clients.add(ws)

      // Send welcome message
      this.sendToClient(ws, {
        type: 'connection',
        data: { message: 'Connected to NEPSE Advanced Charting WebSocket' },
        timestamp: new Date().toISOString()
      })

      // Handle messages from client
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString())
          this.handleClientMessage(ws, message)
        } catch (error) {
          console.error('Invalid WebSocket message:', error)
          this.sendToClient(ws, {
            type: 'error',
            data: { message: 'Invalid message format' },
            timestamp: new Date().toISOString()
          })
        }
      })

      // Handle client disconnect
      ws.on('close', () => {
        console.log('📡 WebSocket client disconnected')
        this.clients.delete(ws)
      })

      // Handle errors
      ws.on('error', (error) => {
        console.error('WebSocket error:', error)
        this.clients.delete(ws)
      })

      // Send ping to keep connection alive
      ws.on('pong', () => {
        // Client responded to ping
      })
    })

    // Start heartbeat
    this.startHeartbeat()

    console.log('✅ WebSocket service initialized')
  }

  private handleClientMessage(ws: WebSocket, message: any): void {
    switch (message.type) {
      case 'subscribe':
        this.handleSubscription(ws, message.data)
        break
      case 'unsubscribe':
        this.handleUnsubscription(ws, message.data)
        break
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          data: {},
          timestamp: new Date().toISOString()
        })
        break
      default:
        this.sendToClient(ws, {
          type: 'error',
          data: { message: 'Unknown message type' },
          timestamp: new Date().toISOString()
        })
    }
  }

  private handleSubscription(ws: WebSocket, data: any): void {
    // Handle subscription to specific symbols, patterns, etc.
    console.log('Client subscribed to:', data)
    
    this.sendToClient(ws, {
      type: 'subscription_confirmed',
      data: { subscribed: data },
      timestamp: new Date().toISOString()
    })
  }

  private handleUnsubscription(ws: WebSocket, data: any): void {
    // Handle unsubscription
    console.log('Client unsubscribed from:', data)
    
    this.sendToClient(ws, {
      type: 'unsubscription_confirmed',
      data: { unsubscribed: data },
      timestamp: new Date().toISOString()
    })
  }

  private sendToClient(ws: WebSocket, message: any): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    }
  }

  public broadcast(message: any): void {
    const messageString = JSON.stringify(message)
    
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageString)
      } else {
        this.clients.delete(client)
      }
    })
  }

  public broadcastPriceUpdate(symbol: string, priceData: any): void {
    this.broadcast({
      type: 'price_update',
      data: {
        symbol,
        ...priceData
      },
      timestamp: new Date().toISOString()
    })
  }

  public broadcastPatternDetection(symbol: string, pattern: any): void {
    this.broadcast({
      type: 'pattern_detected',
      data: {
        symbol,
        pattern
      },
      timestamp: new Date().toISOString()
    })
  }

  public broadcastMarketStatus(status: any): void {
    this.broadcast({
      type: 'market_status',
      data: status,
      timestamp: new Date().toISOString()
    })
  }

  private startHeartbeat(): void {
    const interval = parseInt(process.env.WS_HEARTBEAT_INTERVAL || '30000')
    
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.ping()
        } else {
          this.clients.delete(client)
        }
      })
    }, interval)
  }

  public getClientCount(): number {
    return this.clients.size
  }

  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.close(1000, 'Server shutting down')
      }
    })

    this.clients.clear()
    console.log('✅ WebSocket service shut down')
  }
}

export default WebSocketService
