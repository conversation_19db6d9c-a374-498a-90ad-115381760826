import { createClient, RedisClientType } from 'redis'
import dotenv from 'dotenv'

dotenv.config()

export class RedisService {
  private client: RedisClientType | null = null

  constructor() {
    this.client = createClient({
      socket: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
      password: process.env.REDIS_PASSWORD || undefined,
    })

    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err)
    })

    this.client.on('connect', () => {
      console.log('Redis Client Connected')
    })

    this.client.on('ready', () => {
      console.log('Redis Client Ready')
    })

    this.client.on('end', () => {
      console.log('Redis Client Disconnected')
    })
  }

  async connect(): Promise<void> {
    try {
      if (this.client && !this.client.isOpen) {
        await this.client.connect()
      }
    } catch (error) {
      console.warn('Redis connection failed (continuing without cache):', error.message)
      // Don't throw error, just continue without Redis
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.client && this.client.isOpen) {
        await this.client.disconnect()
      }
    } catch (error) {
      console.error('Redis disconnection failed:', error)
      throw error
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      if (!this.client || !this.client.isOpen) {
        return null // Return null if Redis not available
      }
      return await this.client.get(key)
    } catch (error) {
      console.error('Redis GET error:', error)
      return null
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (!this.client || !this.client.isOpen) {
        return false // Return false if Redis not available
      }

      if (ttl) {
        await this.client.setEx(key, ttl, value)
      } else {
        await this.client.set(key, value)
      }
      return true
    } catch (error) {
      console.error('Redis SET error:', error)
      return false
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }

      const result = await this.client.del(key)
      return result > 0
    } catch (error) {
      console.error('Redis DEL error:', error)
      return false
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }

      const result = await this.client.exists(key)
      return result > 0
    } catch (error) {
      console.error('Redis EXISTS error:', error)
      return false
    }
  }

  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Redis GET JSON error:', error)
      return null
    }
  }

  async setJSON<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value)
      return await this.set(key, jsonString, ttl)
    } catch (error) {
      console.error('Redis SET JSON error:', error)
      return false
    }
  }

  async hGet(key: string, field: string): Promise<string | null> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }
      return await this.client.hGet(key, field)
    } catch (error) {
      console.error('Redis HGET error:', error)
      return null
    }
  }

  async hSet(key: string, field: string, value: string): Promise<boolean> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }

      const result = await this.client.hSet(key, field, value)
      return result > 0
    } catch (error) {
      console.error('Redis HSET error:', error)
      return false
    }
  }

  async hGetAll(key: string): Promise<Record<string, string> | null> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }
      return await this.client.hGetAll(key)
    } catch (error) {
      console.error('Redis HGETALL error:', error)
      return null
    }
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }

      const result = await this.client.expire(key, seconds)
      return result
    } catch (error) {
      console.error('Redis EXPIRE error:', error)
      return false
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }
      return await this.client.keys(pattern)
    } catch (error) {
      console.error('Redis KEYS error:', error)
      return []
    }
  }

  // Cache helper methods
  generateCacheKey(prefix: string, ...parts: string[]): string {
    return `${prefix}:${parts.join(':')}`
  }

  async cacheStockData(symbol: string, timeframe: string, data: any, ttl: number = 300): Promise<boolean> {
    const key = this.generateCacheKey('stock', symbol, timeframe)
    return await this.setJSON(key, data, ttl)
  }

  async getCachedStockData<T>(symbol: string, timeframe: string): Promise<T | null> {
    const key = this.generateCacheKey('stock', symbol, timeframe)
    return await this.getJSON<T>(key)
  }

  async cacheIndicatorData(symbol: string, indicator: string, data: any, ttl: number = 3600): Promise<boolean> {
    const key = this.generateCacheKey('indicator', symbol, indicator)
    return await this.setJSON(key, data, ttl)
  }

  async getCachedIndicatorData<T>(symbol: string, indicator: string): Promise<T | null> {
    const key = this.generateCacheKey('indicator', symbol, indicator)
    return await this.getJSON<T>(key)
  }

  async cachePatternData(symbol: string, timeframe: string, data: any, ttl: number = 1800): Promise<boolean> {
    const key = this.generateCacheKey('patterns', symbol, timeframe)
    return await this.setJSON(key, data, ttl)
  }

  async getCachedPatternData<T>(symbol: string, timeframe: string): Promise<T | null> {
    const key = this.generateCacheKey('patterns', symbol, timeframe)
    return await this.getJSON<T>(key)
  }

  async invalidateCache(pattern: string): Promise<number> {
    try {
      const keys = await this.keys(pattern)
      if (keys.length === 0) return 0

      if (!this.client || !this.client.isOpen) {
        throw new Error('Redis client not connected')
      }

      return await this.client.del(keys)
    } catch (error) {
      console.error('Redis cache invalidation error:', error)
      return 0
    }
  }
}

export default RedisService
