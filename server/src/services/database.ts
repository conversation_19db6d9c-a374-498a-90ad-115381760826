import mysql from 'mysql2/promise'
import dotenv from 'dotenv'

dotenv.config()

export class DatabaseService {
  private connection: mysql.Connection | null = null
  private pool: mysql.Pool | null = null

  constructor() {
    this.pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'nepse_market',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    })
  }

  async connect(): Promise<void> {
    try {
      this.connection = await this.pool!.getConnection()
      console.log('Database connection established')
    } catch (error) {
      console.error('Database connection failed:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.connection) {
        this.connection.release()
        this.connection = null
      }
      if (this.pool) {
        await this.pool.end()
        this.pool = null
      }
      console.log('Database connection closed')
    } catch (error) {
      console.error('Error closing database connection:', error)
      throw error
    }
  }

  async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    try {
      if (!this.pool) {
        throw new Error('Database pool not initialized')
      }

      const [rows] = await this.pool.execute(sql, params)
      return rows as T[]
    } catch (error) {
      console.error('Database query error:', error)
      throw error
    }
  }

  async queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
    const results = await this.query<T>(sql, params)
    return results.length > 0 ? results[0] : null
  }

  async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    if (!this.pool) {
      throw new Error('Database pool not initialized')
    }

    const connection = await this.pool.getConnection()

    try {
      await connection.beginTransaction()
      const result = await callback(connection)
      await connection.commit()
      return result
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Helper methods for common queries
  async getCompanies(filters?: {
    search?: string
    sector_id?: number
    is_active?: boolean
    limit?: number
    offset?: number
  }): Promise<any[]> {
    let sql = 'SELECT * FROM Companies WHERE 1=1'
    const params: any[] = []

    if (filters?.search) {
      sql += ' AND (name LIKE ? OR symbol LIKE ?)'
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    if (filters?.sector_id) {
      sql += ' AND sector_id = ?'
      params.push(filters.sector_id)
    }

    if (filters?.is_active !== undefined) {
      sql += ' AND is_active = ?'
      params.push(filters.is_active)
    }

    sql += ' ORDER BY symbol'

    if (filters?.limit) {
      sql += ` LIMIT ${Number(filters.limit)}`

      if (filters?.offset) {
        sql += ` OFFSET ${Number(filters.offset)}`
      }
    }

    return this.query(sql, params)
  }

  async getStockPrices(
    symbol: string,
    filters?: {
      start_date?: string
      end_date?: string
      limit?: number
    }
  ): Promise<any[]> {
    let sql = `
      SELECT sp.*, c.symbol, c.name
      FROM Stock_Prices sp
      JOIN Companies c ON sp.company_id = c.company_id
      WHERE c.symbol = ?
    `
    const params: any[] = [symbol]

    if (filters?.start_date) {
      sql += ' AND sp.date >= ?'
      params.push(filters.start_date)
    }

    if (filters?.end_date) {
      sql += ' AND sp.date <= ?'
      params.push(filters.end_date)
    }

    sql += ' ORDER BY sp.date DESC'

    if (filters?.limit) {
      sql += ` LIMIT ${Number(filters.limit)}`
    }

    return this.query(sql, params)
  }

  async getTechnicalIndicators(
    symbol: string,
    indicator_type: string,
    filters?: {
      start_date?: string
      end_date?: string
      limit?: number
    }
  ): Promise<any[]> {
    let sql = `
      SELECT ti.*, c.symbol, c.name
      FROM Technical_Indicators ti
      JOIN Companies c ON ti.company_id = c.company_id
      WHERE c.symbol = ? AND ti.indicator_type = ?
    `
    const params: any[] = [symbol, indicator_type]

    if (filters?.start_date) {
      sql += ' AND ti.date >= ?'
      params.push(filters.start_date)
    }

    if (filters?.end_date) {
      sql += ' AND ti.date <= ?'
      params.push(filters.end_date)
    }

    sql += ' ORDER BY ti.date DESC'

    if (filters?.limit) {
      sql += ' LIMIT ?'
      params.push(filters.limit)
    }

    return this.query(sql, params)
  }

  async insertTechnicalIndicator(data: {
    company_id: number
    date: string
    indicator_type: string
    value: number
    parameters?: any
    purpose?: string
  }): Promise<void> {
    const sql = `
      INSERT INTO Technical_Indicators
      (company_id, date, indicator_type, value, parameters, purpose)
      VALUES (?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.company_id,
      data.date,
      data.indicator_type,
      data.value,
      data.parameters ? JSON.stringify(data.parameters) : null,
      data.purpose || 'CACHE'
    ]

    await this.query(sql, params)
  }

  async getCompanyBySymbol(symbol: string): Promise<any | null> {
    const sql = 'SELECT * FROM Companies WHERE symbol = ? AND is_active = 1'
    return this.queryOne(sql, [symbol])
  }

  async getLatestStockPrice(symbol: string): Promise<any | null> {
    const sql = `
      SELECT sp.*, c.symbol, c.name
      FROM Stock_Prices sp
      JOIN Companies c ON sp.company_id = c.company_id
      WHERE c.symbol = ?
      ORDER BY sp.date DESC
      LIMIT 1
    `
    return this.queryOne(sql, [symbol])
  }
}

export default DatabaseService
