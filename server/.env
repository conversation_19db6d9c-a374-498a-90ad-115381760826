# Server Configuration
PORT=5001
NODE_ENV=development

# Database Configuration (using existing NEPSE database)
DB_HOST=localhost
DB_PORT=3306
DB_USER=nepse_user
DB_PASSWORD=nepse_password
DB_NAME=nepse_market

# Redis Configuration (optional - will work without Redis)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=nepse_advanced_charting_super_secret_key_2024
JWT_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# Technical Analysis
TA_CACHE_DURATION=3600
PATTERN_DETECTION_ENABLED=true
ICT_ANALYSIS_ENABLED=true
SMC_ANALYSIS_ENABLED=true
