{"name": "nepse-charting-server", "version": "1.0.0", "description": "Backend server for NEPSE Advanced Charting Application", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "mysql2": "^3.6.5", "redis": "^4.6.11", "ws": "^8.14.2", "dotenv": "^16.3.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2", "technicalindicators": "^3.1.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/ws": "^8.5.10", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/node": "^20.9.0", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "tsx": "^4.1.4", "typescript": "^5.2.2"}}