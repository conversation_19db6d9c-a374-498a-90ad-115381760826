# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=nepse_market

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# API Keys
NEPSE_API_KEY=your_nepse_api_key
LIVE_DATA_API_KEY=your_live_data_api_key

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# Technical Analysis
TA_CACHE_DURATION=3600
PATTERN_DETECTION_ENABLED=true
ICT_ANALYSIS_ENABLED=true
SMC_ANALYSIS_ENABLED=true
